<?php
/**
 * Configurações PIX Centralizadas - Sistema kesung-site
 * VERSÃO SEGURA - Credenciais via .env
 */

// Carregar variáveis de ambiente
require_once __DIR__ . '/../includes/env_loader.php';

// Carregar conexão com banco
require_once __DIR__ . '/../database/connection.php';

// Buscar credenciais do banco de dados
try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    $stmt = $pdo->query("SELECT * FROM payment_credentials LIMIT 1");
    $credentials = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $credentials = false;
}

// Configurações do Asaas - prioridade: banco > .env > fallback
if ($credentials && !empty($credentials['api_key'])) {
    // Usar credenciais do banco
    $pixApiKey = $credentials['api_key'];
    $environment = $credentials['environment'] ?? 'production';
    $apiUrl = $environment === 'production' ? 'https://api.asaas.com/v3' : 'https://api-sandbox.asaas.com/v3';
} else {
    // Fallback para .env ou padrão
    $pixApiKey = EnvLoader::get('PIX_API_KEY', '');
    if (empty($pixApiKey)) {
        // Fallback final
        $pixApiKey = 'aact_prod_000MzkwODA2MWY2OGM3MWRlMDU2NWM3MzJlNzZmNGZhZGY6OjM5YzEzNmY0LTJkODEtNDZlMC05ODBjLTM0NTc4ZmJlNmVhYTo6JGFhY2hfMjUwZjA2YWYtY2FkOS00YzQ0LWIxYzItN2YzMWViMzZjZjFk';
    }
    $apiUrl = EnvLoader::get('PIX_API_URL', 'https://api.asaas.com/v3');
}

define('PIX_ASAAS_API_URL', $apiUrl);
define('PIX_ASAAS_API_KEY', $pixApiKey);

// Configurações gerais do PIX
define('PIX_TIMEOUT_MINUTES', EnvLoader::get('PIX_TIMEOUT', 15));
define('PIX_CHECK_INTERVAL_SECONDS', 5);
define('PIX_MAX_CHECK_ATTEMPTS', 120); // 10 minutos

// Configurações de webhook (usando tokens do .env)
define('PIX_WEBHOOK_URL', 'https://' . $_SERVER['HTTP_HOST'] . '/webhook_v2.php');
define('PIX_WEBHOOK_TOKEN', EnvLoader::get('WEBHOOK_TOKEN', ''));

// Configurações de email
define('PIX_EMAIL_FROM', '<EMAIL>');
define('PIX_EMAIL_REPLY_TO', '<EMAIL>');
define('PIX_EMAIL_SUBJECT', 'Seu produto está pronto para download');

// Configurações de logs
define('PIX_LOG_DIR', __DIR__ . '/../logs');
define('PIX_LOG_MAX_SIZE', 10 * 1024 * 1024); // 10MB

// Status mapping
$PIX_STATUS_MAP = [
    'PENDING' => 'pending',
    'RECEIVED' => 'paid',
    'CONFIRMED' => 'paid',
    'OVERDUE' => 'failed',
    'REFUNDED' => 'failed',
    'RECEIVED_IN_CASH' => 'paid',
    'AWAITING_RISK_ANALYSIS' => 'processing'
];

// Função para obter headers do Asaas
function getPixAsaasHeaders() {
    return [
        'Content-Type: application/json',
        'access_token: ' . PIX_ASAAS_API_KEY,
        'User-Agent: kesung-site/1.0'
    ];
}

// Função para log PIX
function logPix($message, $level = 'INFO') {
    if (!is_dir(PIX_LOG_DIR)) {
        mkdir(PIX_LOG_DIR, 0755, true);
    }
    
    $logFile = PIX_LOG_DIR . '/pix_' . date('Y-m-d') . '.log';
    
    // Rotacionar log se muito grande
    if (file_exists($logFile) && filesize($logFile) > PIX_LOG_MAX_SIZE) {
        rename($logFile, $logFile . '.old');
    }
    
    $timestamp = date('[Y-m-d H:i:s]');
    $logEntry = "$timestamp [$level] $message" . PHP_EOL;
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

// Função para validar CPF
function validateCPF($cpf) {
    $cpf = preg_replace('/[^0-9]/', '', $cpf);
    
    if (strlen($cpf) != 11) return false;
    if (preg_match('/(\d)\1{10}/', $cpf)) return false;
    
    for ($t = 9; $t < 11; $t++) {
        for ($d = 0, $c = 0; $c < $t; $c++) {
            $d += $cpf[$c] * (($t + 1) - $c);
        }
        $d = ((10 * $d) % 11) % 10;
        if ($cpf[$c] != $d) return false;
    }
    
    return true;
}

// Função para validar CNPJ
function validateCNPJ($cnpj) {
    $cnpj = preg_replace('/[^0-9]/', '', $cnpj);
    
    if (strlen($cnpj) != 14) return false;
    if (preg_match('/(\d)\1{13}/', $cnpj)) return false;
    
    $weights1 = [5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
    $weights2 = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
    
    $sum = 0;
    for ($i = 0; $i < 12; $i++) {
        $sum += $cnpj[$i] * $weights1[$i];
    }
    $digit1 = ($sum % 11) < 2 ? 0 : 11 - ($sum % 11);
    
    $sum = 0;
    for ($i = 0; $i < 13; $i++) {
        $sum += $cnpj[$i] * $weights2[$i];
    }
    $digit2 = ($sum % 11) < 2 ? 0 : 11 - ($sum % 11);
    
    return $cnpj[12] == $digit1 && $cnpj[13] == $digit2;
}

// Função para validar documento (CPF ou CNPJ)
function validateDocument($document) {
    $document = preg_replace('/[^0-9]/', '', $document);
    
    if (strlen($document) == 11) {
        return validateCPF($document);
    } elseif (strlen($document) == 14) {
        return validateCNPJ($document);
    }
    
    return false;
}

// Função para formatar valor monetário
function formatPixAmount($amount) {
    return number_format($amount, 2, ',', '.');
}

// Função para gerar referência externa única
function generatePixReference($productId) {
    return "PIX_" . uniqid() . "_" . $productId . "_" . time();
}

// Função para calcular data de expiração
function getPixExpirationDate($minutes = null) {
    $minutes = $minutes ?: PIX_TIMEOUT_MINUTES;
    return date('Y-m-d H:i:s', strtotime("+$minutes minutes"));
}

// Função para verificar se pagamento expirou
function isPixExpired($expiresAt) {
    return strtotime($expiresAt) < time();
}

// Função para mapear status do Asaas
function mapPixStatus($asaasStatus) {
    global $PIX_STATUS_MAP;
    return $PIX_STATUS_MAP[$asaasStatus] ?? 'unknown';
}

// Configurações de resposta JSON
function sendPixResponse($success, $message, $data = null, $httpCode = 200) {
    http_response_code($httpCode);
    
    $response = [
        'success' => $success,
        'message' => $message,
        'timestamp' => date('c'),
        'system' => 'kesung-site-pix'
    ];
    
    if ($data !== null) {
        if (is_array($data)) {
            $response = array_merge($response, $data);
        } else {
            $response['data'] = $data;
        }
    }
    
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit;
}

// Função para sanitizar dados de entrada
function sanitizePixInput($data) {
    if (is_array($data)) {
        return array_map('sanitizePixInput', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

// Verificar se todas as constantes foram definidas
function validatePixConfig() {
    $required = [
        'PIX_ASAAS_API_URL',
        'PIX_ASAAS_API_KEY',
        'PIX_TIMEOUT_MINUTES',
        'PIX_WEBHOOK_URL'
    ];
    
    foreach ($required as $const) {
        if (!defined($const)) {
            throw new Exception("Configuração PIX incompleta: $const não definida");
        }
    }
    
    return true;
}

// Inicializar configurações
try {
    validatePixConfig();
    logPix("Configurações PIX carregadas com sucesso");
} catch (Exception $e) {
    logPix("ERRO nas configurações PIX: " . $e->getMessage(), 'ERROR');
    throw $e;
}
?>
