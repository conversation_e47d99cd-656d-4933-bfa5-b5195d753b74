# Sistema CSS Padronizado - Admin Panel

## 📋 Visão Geral

O sistema admin foi completamente padronizado com um conjunto de arquivos CSS organizados e otimizados para garantir consistência visual e responsividade em todas as páginas.

## 📁 Estrutura dos Arquivos CSS

### Arquivos Principais

1. **`standardized.css`** - CSS principal padronizado
   - Variáveis CSS globais
   - Reset e base
   - Botões padronizados
   - Cards padronizados
   - Formulários padronizados
   - Tabelas padronizadas
   - Badges padronizados
   - Sidebar e navegação
   - Modais padronizados
   - Alertas padronizados
   - Paginação padronizada
   - Responsividade completa

2. **`style.css`** - Estilos base e complementares
   - Variáveis CSS
   - Layout específico
   - Componentes específicos
   - Estilos para páginas específicas

3. **`dashboard.css`** - Estilos específicos do dashboard
   - Cards do dashboard
   - Tabelas do dashboard
   - Responsividade específica

### Arquivos Complementares

4. **`assets/css/style.css`** - Estilos complementares
   - Navegação sidebar específica
   - Modais customizados

5. **`assets/css/admin.css`** - CSS admin complementar
   - Layout wrapper
   - Sidebar específico
   - Classes utilitárias

## 🎨 Padrão de Cores

```css
:root {
    --primary-color: #198754;      /* Verde principal */
    --primary-hover: #157347;      /* Verde hover */
    --secondary-color: #6c757d;    /* Cinza secundário */
    --light-color: #f8f9fa;        /* Cinza claro */
    --dark-color: #212529;         /* Preto/cinza escuro */
    --success-color: #28a745;      /* Verde sucesso */
    --danger-color: #dc3545;       /* Vermelho perigo */
    --warning-color: #ffc107;      /* Amarelo aviso */
    --info-color: #17a2b8;         /* Azul informação */
    --white-color: #ffffff;        /* Branco */
    --border-color: #e2e8f0;       /* Cinza borda */
}
```

## 📐 Padrões de Tamanhos

### Botões
- **Padrão**: 40px altura, 100px largura mínima
- **Pequeno**: 32px altura, 80px largura mínima
- **Grande**: 48px altura, 140px largura mínima
- **Ação em tabela**: 28px altura, 60px largura mínima

### Cards
- **Border radius**: 12px
- **Padding**: 20px
- **Margin bottom**: 20px
- **Box shadow**: 0 4px 6px rgba(0,0,0,0.1)

### Formulários
- **Input height**: 42px
- **Padding**: 10px 14px
- **Border radius**: 8px

### Tabelas
- **Header height**: 45px
- **Row height**: 50px
- **Padding**: 12px 10px

## 📱 Responsividade

### Breakpoints
- **Desktop Large**: ≥1200px
- **Desktop**: ≥992px
- **Tablet**: ≤991.98px
- **Mobile**: ≤767.98px
- **Mobile Small**: ≤575.98px

### Comportamento Responsivo

#### Desktop (≥992px)
- Sidebar sempre visível
- Cards em grid responsivo
- Tabelas com todas as colunas

#### Tablet (≤991.98px)
- Sidebar colapsável
- Cards menores
- Algumas colunas de tabela ocultas

#### Mobile (≤767.98px)
- Sidebar overlay
- Cards em coluna única
- Tabelas com colunas essenciais apenas
- Botões e inputs menores

## 🔧 Classes Utilitárias

### Espaçamentos
```css
.mb-2 { margin-bottom: 8px; }
.mb-3 { margin-bottom: 16px; }
.mb-4 { margin-bottom: 24px; }
.mb-5 { margin-bottom: 32px; }
```

### Estados
```css
.loading { opacity: 0.6; pointer-events: none; }
.fade-in { animation: fadeIn 0.5s ease-in; }
```

### Texto
```css
.text-truncate-custom { 
    white-space: nowrap; 
    overflow: hidden; 
    text-overflow: ellipsis; 
    max-width: 150px; 
}
```

## 🎯 Componentes Padronizados

### Dashboard Cards
- Ícones coloridos com background suave
- Números grandes e legíveis
- Labels descritivas
- Hover effects suaves

### Tabelas
- Headers com background cinza claro
- Hover effects nas linhas
- Botões de ação compactos
- Badges coloridos para status

### Sidebar
- Itens com altura fixa (50px)
- Ícones alinhados
- Estados hover e active
- Transições suaves

### Modais
- Headers com background
- Botões padronizados
- Responsividade completa
- Animações de entrada

## 🚀 Como Usar

### 1. Ordem de Carregamento dos CSS
```html
<!-- Bootstrap CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

<!-- Font Awesome -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">

<!-- CSS Customizado - ORDEM IMPORTANTE -->
<link href="css/style.css" rel="stylesheet">
<link rel="stylesheet" href="assets/css/style.css">
<link href="css/standardized.css" rel="stylesheet">
<link href="css/dashboard.css" rel="stylesheet"> <!-- Apenas no dashboard -->
```

### 2. Classes Recomendadas

#### Para Cards
```html
<div class="card">
    <div class="card-header">
        <h5 class="card-title">Título</h5>
    </div>
    <div class="card-body">
        Conteúdo
    </div>
</div>
```

#### Para Tabelas
```html
<div class="table-responsive">
    <table class="table table-hover">
        <thead>
            <tr>
                <th>Coluna 1</th>
                <th>Coluna 2</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Dados</td>
                <td>
                    <button class="btn btn-sm btn-primary btn-action">
                        <i class="fas fa-edit"></i>
                    </button>
                </td>
            </tr>
        </tbody>
    </table>
</div>
```

#### Para Dashboard Cards
```html
<div class="dashboard-card">
    <div class="card-body">
        <div class="card-icon sales-icon">
            <i class="fas fa-chart-line"></i>
        </div>
        <div class="card-content">
            <h3>1,234</h3>
            <p>Total de Vendas</p>
        </div>
    </div>
</div>
```

## ✅ Benefícios da Padronização

1. **Consistência Visual**: Todos os elementos têm o mesmo estilo
2. **Responsividade**: Funciona perfeitamente em todos os dispositivos
3. **Manutenibilidade**: Fácil de manter e atualizar
4. **Performance**: CSS otimizado e organizado
5. **Acessibilidade**: Melhor contraste e navegação
6. **Escalabilidade**: Fácil de adicionar novos componentes

## 🔄 Atualizações Futuras

Para manter a padronização:

1. **Sempre use as classes existentes** antes de criar novas
2. **Siga os padrões de nomenclatura** estabelecidos
3. **Teste a responsividade** em todos os breakpoints
4. **Mantenha a consistência** de cores e tamanhos
5. **Documente** novas classes adicionadas

---

**Nota**: Este sistema foi desenvolvido mantendo todas as lógicas PHP existentes intactas, focando apenas na padronização visual e responsividade.
