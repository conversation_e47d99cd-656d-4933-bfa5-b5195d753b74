<?php
require_once 'includes/auth_check.php';
require_once 'includes/header.php';

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // Primeiro, sincronizar clientes da tabela orders para customers
    $stmt = $pdo->query("
        INSERT IGNORE INTO customers (name, email, whatsapp, created_at)
        SELECT DISTINCT
            o.customer_name,
            o.customer_email,
            '',
            NOW()
        FROM orders o
        WHERE o.customer_email NOT IN (SELECT email FROM customers)
    ");

    // Get customers with their order counts and total spent
    $stmt = $pdo->query("
        SELECT
            c.id,
            c.name as customer_name,
            c.email as customer_email,
            c.whatsapp as customer_whatsapp,
            COALESCE(COUNT(o.id), 0) as total_orders,
            COALESCE(SUM(CASE WHEN o.payment_status = 'approved' THEN o.total_amount ELSE 0 END), 0) as total_spent,
            MAX(o.created_at) as last_order
        FROM customers c
        LEFT JOIN orders o ON c.email = o.customer_email
        GROUP BY c.id, c.name, c.email, c.whatsapp
        ORDER BY total_spent DESC, last_order DESC
    ");
    $customers = $stmt->fetchAll();
} catch (Exception $e) {
    $error = $e->getMessage();
}
?>

<div class="container-fluid px-4">
    <h2 class="fs-2 mb-4">Clientes</h2>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
    <?php endif; ?>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-users me-1"></i>
            Lista de Clientes
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover table-customers">
                    <thead>
                        <tr>
                            <th>Nome</th>
                            <th>Email</th>
                            <th>WhatsApp</th>
                            <th>Total de Pedidos</th>
                            <th>Total Gasto</th>
                            <th>Último Pedido</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($customers): foreach ($customers as $customer): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($customer['customer_name'] ?? ''); ?></td>
                                <td><?php echo htmlspecialchars($customer['customer_email'] ?? ''); ?></td>
                                <td><?php echo htmlspecialchars($customer['customer_whatsapp'] ?? ''); ?></td>
                                <td><?php echo $customer['total_orders'] ?? 0; ?></td>
                                <td>R$ <?php echo number_format($customer['total_spent'] ?? 0, 2, ',', '.'); ?></td>
                                <td><?php echo $customer['last_order'] ? date('d/m/Y H:i', strtotime($customer['last_order'])) : '-'; ?></td>
                                <td>
                                    <a href="view_customer.php?email=<?php echo urlencode($customer['customer_email']); ?>" 
                                       class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
