<?php
require_once 'includes/auth_check.php';

// Buscar dados para os relatórios
$db = Database::getInstance();
$pdo = $db->getConnection();

// Buscar totais do banco de dados
$sql_totals = "SELECT
    COUNT(DISTINCT CASE WHEN o.payment_status = 'paid' THEN o.id END) as total_sales,
    SUM(CASE WHEN o.payment_status = 'paid' THEN o.amount ELSE 0 END) as total_revenue,
    COUNT(DISTINCT p.id) as total_products,
    COUNT(DISTINCT c.id) as total_customers
FROM orders o
LEFT JOIN products p ON 1=1
LEFT JOIN customers c ON o.customer_id = c.id";

$stmt_totals = $pdo->prepare($sql_totals);
$stmt_totals->execute();
$totals = $stmt_totals->fetch(PDO::FETCH_ASSOC);

$totalSales = $totals['total_sales'] ?? 0;
$totalRevenue = $totals['total_revenue'] ?? 0;
$totalProducts = $totals['total_products'] ?? 0;
$totalCustomers = $totals['total_customers'] ?? 0;

// Total de vendas e receita
$stmt = $pdo->query("
    SELECT
        COUNT(*) as total_sales,
        SUM(CASE WHEN payment_status = 'paid' THEN amount ELSE 0 END) as total_revenue,
        SUM(CASE WHEN payment_status = 'pending' THEN 1 ELSE 0 END) as pending_sales,
        SUM(CASE WHEN payment_status = 'paid' THEN 1 ELSE 0 END) as completed_sales
    FROM orders
");
$salesData = $stmt->fetch(PDO::FETCH_ASSOC);

// Vendas por status
$stmt = $pdo->query("SELECT payment_status, COUNT(*) as count FROM orders GROUP BY payment_status");
$salesByStatus = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Produtos mais vendidos
$stmt = $pdo->query("
    SELECT
        p.name,
        COUNT(o.id) as sales_count,
        SUM(o.amount) as total_revenue,
        p.price as unit_price
    FROM orders o
    JOIN products p ON o.product_id = p.id
    WHERE o.payment_status = 'paid'
    GROUP BY p.id, p.name, p.price
    ORDER BY sales_count DESC
    LIMIT 5
");
$topProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Vendas dos últimos 7 dias
$stmt = $pdo->query("
    SELECT
        DATE(created_at) as date,
        COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as count,
        SUM(CASE WHEN payment_status = 'paid' THEN amount ELSE 0 END) as revenue
    FROM orders
    WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
    GROUP BY DATE(created_at)
    ORDER BY date DESC
");
$recentSales = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Preparar dados para os gráficos
$chartLabels = [];
$chartData = [];
$chartRevenue = [];

foreach ($recentSales as $sale) {
    $chartLabels[] = date('d/m', strtotime($sale['date']));
    $chartData[] = $sale['count'];
    $chartRevenue[] = $sale['revenue'];
}

// Preparar dados para o gráfico de status
$statusLabels = [];
$statusData = [];
$statusColors = [
    'approved' => '#28a745',
    'pending' => '#ffc107',
    'cancelled' => '#dc3545',
    'refunded' => '#17a2b8'
];

foreach ($salesByStatus as $status) {
    $statusLabels[] = ucfirst($status['payment_status']);
    $statusData[] = $status['count'];
}

// Buscar vendas por data/hora dos últimos 7 dias
$sql_sales_by_hour = "
    SELECT
        DATE_FORMAT(created_at, '%Y-%m-%d %H:00:00') as hour_date,
        COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as total_sales,
        SUM(CASE WHEN payment_status = 'paid' THEN amount ELSE 0 END) as revenue
    FROM orders
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    GROUP BY hour_date
    ORDER BY hour_date ASC";

$stmt_hours = $pdo->prepare($sql_sales_by_hour);
$stmt_hours->execute();
$sales_by_hour = $stmt_hours->fetchAll(PDO::FETCH_ASSOC);

$hourLabels = [];
$hourSales = [];
$hourRevenue = [];

foreach ($sales_by_hour as $sale) {
    $hourLabels[] = date('d/m H:i', strtotime($sale['hour_date']));
    $hourSales[] = $sale['total_sales'];
    $hourRevenue[] = floatval($sale['revenue']);
}

// Buscar status das vendas
$sql_status = "
    SELECT
        payment_status as status,
        COUNT(*) as total,
        SUM(amount) as total_amount
    FROM orders
    GROUP BY payment_status";

$stmt_status = $pdo->prepare($sql_status);
$stmt_status->execute();
$status_data = $stmt_status->fetchAll(PDO::FETCH_ASSOC);

$statusLabels = [];
$statusValues = [];
$statusAmounts = [];
$statusColors = [
    'approved' => '#28a745',
    'pending' => '#ffc107',
    'cancelled' => '#dc3545',
    'refunded' => '#17a2b8'
];

foreach ($status_data as $status) {
    $label = ucfirst($status['status']);
    $statusLabels[] = $label;
    $statusValues[] = $status['total'];
    $statusAmounts[] = $status['total_amount'];
}

// Não há tabela users, vamos pular esta parte
$userStatusLabels = [];
$userStatusValues = [];

// Buscar vendas dos últimos 7 dias por hora
$sql_sales_by_hour = "
    SELECT
        DATE_FORMAT(created_at, '%Y-%m-%d %H:00:00') as hour_date,
        COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as total_sales,
        SUM(CASE WHEN payment_status = 'paid' THEN amount ELSE 0 END) as revenue
    FROM orders
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    GROUP BY hour_date
    ORDER BY hour_date ASC";

$stmt_hours = $pdo->prepare($sql_sales_by_hour);
$stmt_hours->execute();
$sales_by_hour = $stmt_hours->fetchAll(PDO::FETCH_ASSOC);

$hourLabels = [];
$hourSales = [];
$hourRevenue = [];

foreach ($sales_by_hour as $sale) {
    $hourLabels[] = date('d/m H:i', strtotime($sale['hour_date']));
    $hourSales[] = $sale['total_sales'];
    $hourRevenue[] = floatval($sale['revenue']);
}

// Estatísticas dos clientes com COALESCE para evitar nulos
$sql_customer_stats = "
    SELECT 
        COUNT(DISTINCT c.id) as total_customers,
        COUNT(DISTINCT CASE WHEN o.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN c.id END) as active_customers,
        COALESCE(AVG(NULLIF(order_counts.order_count, 0)), 0) as avg_orders_per_customer,
        COALESCE(AVG(NULLIF(order_counts.total_spent, 0)), 0) as avg_spent_per_customer
    FROM customers c
    LEFT JOIN (
        SELECT 
            customer_id,
            COUNT(*) as order_count,
            COALESCE(SUM(total_amount), 0) as total_spent
        FROM orders
        GROUP BY customer_id
    ) order_counts ON c.id = order_counts.customer_id
    LEFT JOIN orders o ON c.id = o.customer_id";

$stmt_customer_stats = $pdo->prepare($sql_customer_stats);
$stmt_customer_stats->execute();
$customer_stats = $stmt_customer_stats->fetch(PDO::FETCH_ASSOC);



// ... resto do código ...

require_once 'includes/header.php';
?>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- Header -->
<div class="page-header mb-4">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">
                <i class="fas fa-chart-line me-3"></i>
                Relatórios
            </h1>
            <p class="page-subtitle mb-0">Análise completa de vendas e estatísticas</p>
        </div>
    </div>
</div>

<!-- Cards de Estatísticas Principais -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card dashboard-card">
            <div class="card-body">
                <div class="card-icon sales-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="card-content">
                    <h3><?php echo $totalSales; ?></h3>
                    <p>Vendas</p>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card dashboard-card">
            <div class="card-body">
                <div class="card-icon revenue-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="card-content">
                    <h3>R$ <?php echo number_format($totalRevenue, 2, ',', '.'); ?></h3>
                    <p>Receita Total</p>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card dashboard-card">
            <div class="card-body">
                <div class="card-icon products-icon">
                    <i class="fas fa-box"></i>
                </div>
                <div class="card-content">
                    <h3><?php echo $totalProducts; ?></h3>
                    <p>Produtos Ativos</p>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card dashboard-card">
            <div class="card-body">
                <div class="card-icon customers-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="card-content">
                    <h3><?php echo $totalCustomers; ?></h3>
                    <p>Total de Clientes</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Estatísticas de Clientes -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card dashboard-card">
            <div class="card-body">
                <div class="card-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <i class="fas fa-users"></i>
                </div>
                <div class="card-content">
                    <h3><?php echo number_format($customer_stats['total_customers']); ?></h3>
                    <p>Total de Clientes</p>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card dashboard-card">
            <div class="card-body">
                <div class="card-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="card-content">
                    <h3><?php echo number_format($customer_stats['active_customers']); ?></h3>
                    <p>Clientes Ativos (30d)</p>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card dashboard-card">
            <div class="card-body">
                <div class="card-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                    <i class="fas fa-shopping-basket"></i>
                </div>
                <div class="card-content">
                    <h3><?php echo number_format($customer_stats['avg_orders_per_customer'] ?? 0, 1); ?></h3>
                    <p>Média Pedidos/Cliente</p>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card dashboard-card">
            <div class="card-body">
                <div class="card-icon" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="card-content">
                    <h3>R$ <?php echo number_format($customer_stats['avg_spent_per_customer'] ?? 0, 2, ',', '.'); ?></h3>
                    <p>Média Gasto/Cliente</p>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- Status das Vendas -->
<div class="row mb-4">
    <?php foreach ($status_data as $status): ?>
    <div class="col-md-3 mb-3">
        <div class="card dashboard-card">
            <div class="card-body">
                <div class="card-icon" style="background-color: <?php echo $statusColors[$status['status']] ?? '#6c757d'; ?>">
                    <i class="fas fa-shopping-bag"></i>
                </div>
                <div class="card-content">
                    <h3><?php echo $status['total']; ?></h3>
                    <p>Vendas <?php echo ucfirst($status['status']); ?></p>
                    <small class="text-muted">R$ <?php echo number_format($status['total_amount'], 2, ',', '.'); ?></small>
                </div>
            </div>
        </div>
    </div>
    <?php endforeach; ?>
</div>

<!-- Gráficos de Análise -->
<div class="row mb-4">
    <div class="col-lg-8 mb-3">
        <div class="card dashboard-card">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    <i class="fas fa-chart-line me-2"></i>
                    Vendas dos Últimos 7 Dias
                </h5>
                <canvas id="salesChart" height="100"></canvas>
            </div>
        </div>
    </div>

    <div class="col-lg-4 mb-3">
        <div class="card dashboard-card">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    <i class="fas fa-chart-pie me-2"></i>
                    Status das Vendas
                </h5>
                <canvas id="statusChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-lg-8 mb-3">
        <div class="card dashboard-card">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    <i class="fas fa-clock me-2"></i>
                    Vendas por Hora (7 dias)
                </h5>
                <canvas id="salesByHourChart" height="100"></canvas>
            </div>
        </div>
    </div>

    <div class="col-lg-4 mb-3">
        <div class="card dashboard-card">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    <i class="fas fa-chart-doughnut me-2"></i>
                    Distribuição de Status
                </h5>
                <canvas id="statusPieChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-lg-8 mb-3">
        <div class="card dashboard-card">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    <i class="fas fa-chart-bar me-2"></i>
                    Análise por Status
                </h5>
                <canvas id="salesByStatusChart" height="100"></canvas>
            </div>
        </div>
    </div>

    <div class="col-lg-4 mb-3">
        <div class="card dashboard-card">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    <i class="fas fa-users me-2"></i>
                    Status dos Usuários
                </h5>
                <canvas id="userStatusChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Produtos Mais Vendidos -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card dashboard-card">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    <i class="fas fa-trophy me-2"></i>
                    Produtos Mais Vendidos
                </h5>
                <div class="table-responsive">
                    <table class="table table-dashboard">
                                <thead>
                                    <tr>
                                        <th>Produto</th>
                                        <th>Vendas</th>
                                        <th>Preço Unit.</th>
                                        <th>Receita Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($topProducts as $product): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($product['name']); ?></td>
                                        <td><?php echo number_format($product['sales_count']); ?></td>
                                        <td>R$ <?php echo number_format($product['unit_price'], 2, ',', '.'); ?></td>
                                        <td>R$ <?php echo number_format($product['total_revenue'], 2, ',', '.'); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                    <?php if (empty($topProducts)): ?>
                                    <tr>
                                        <td colspan="4" class="text-center">Nenhum produto vendido ainda</td>
                                    </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

<script>
// Verificar se Chart.js foi carregado
if (typeof Chart !== 'undefined') {
    // Gráfico de Vendas
    const salesCtx = document.getElementById('salesChart');
    if (salesCtx) {
        const ctx = salesCtx.getContext('2d');
        const gradientFill = ctx.createLinearGradient(0, 0, 0, 400);
        gradientFill.addColorStop(0, 'rgba(0, 123, 255, 0.3)');
        gradientFill.addColorStop(1, 'rgba(0, 123, 255, 0)');

        const gradientFill2 = ctx.createLinearGradient(0, 0, 0, 400);
        gradientFill2.addColorStop(0, 'rgba(40, 167, 69, 0.3)');
        gradientFill2.addColorStop(1, 'rgba(40, 167, 69, 0)');

        new Chart(ctx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode($chartLabels); ?>,
        datasets: [{
            label: 'Número de Vendas',
            data: <?php echo json_encode($chartData); ?>,
            borderColor: '#007bff',
            backgroundColor: gradientFill,
            tension: 0.4,
            fill: true,
            pointBackgroundColor: '#007bff',
            pointBorderColor: '#fff',
            pointBorderWidth: 2,
            pointRadius: 6,
            pointHoverRadius: 8
        }, {
            label: 'Receita (R$)',
            data: <?php echo json_encode($chartRevenue); ?>,
            borderColor: '#28a745',
            backgroundColor: gradientFill2,
            tension: 0.4,
            fill: true,
            pointBackgroundColor: '#28a745',
            pointBorderColor: '#fff',
            pointBorderWidth: 2,
            pointRadius: 6,
            pointHoverRadius: 8,
            yAxisID: 'revenue'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
            intersect: false,
            mode: 'index'
        },
        plugins: {
            legend: {
                position: 'top',
                labels: {
                    padding: 20,
                    usePointStyle: true,
                    pointStyle: 'circle'
                }
            },
            tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                padding: 12,
                titleColor: '#fff',
                titleFont: {
                    size: 14,
                    weight: 'bold'
                },
                bodyFont: {
                    size: 13
                },
                bodySpacing: 8,
                multiKeyBackground: '#fff',
                displayColors: true,
                callbacks: {
                    label: function(context) {
                        let label = context.dataset.label || '';
                        let value = context.raw;
                        if (label.includes('Receita')) {
                            return `${label}: R$ ${value.toFixed(2)}`;
                        }
                        return `${label}: ${value}`;
                    }
                }
            }
        },
        scales: {
            x: {
                grid: {
                    display: false
                },
                ticks: {
                    font: {
                        size: 12
                    }
                }
            },
            y: {
                beginAtZero: true,
                grid: {
                    borderDash: [8, 4]
                },
                ticks: {
                    font: {
                        size: 12
                    }
                }
            },
            revenue: {
                position: 'right',
                beginAtZero: true,
                grid: {
                    display: false
                },
                ticks: {
                    font: {
                        size: 12
                    },
                    callback: function(value) {
                        return 'R$ ' + value.toFixed(2);
                    }
                }
            }
        },
        animation: {
            duration: 2000,
            easing: 'easeInOutQuart'
        }
    }
});

    // Gráfico de Status
    const statusCtx = document.getElementById('statusChart');
    if (statusCtx) {
        new Chart(statusCtx.getContext('2d'), {
    type: 'doughnut',
    data: {
        labels: <?php echo json_encode($statusLabels); ?>,
        datasets: [{
            data: <?php echo json_encode($statusData); ?>,
            backgroundColor: [
                'rgba(40, 167, 69, 0.8)',   // approved (verde)
                'rgba(255, 193, 7, 0.8)',    // pending (amarelo)
                'rgba(220, 53, 69, 0.8)',    // cancelled (vermelho)
                'rgba(23, 162, 184, 0.8)'    // refunded (azul)
            ],
            borderColor: [
                '#28a745',  // approved
                '#ffc107',  // pending
                '#dc3545',  // cancelled
                '#17a2b8'   // refunded
            ],
            borderWidth: 2,
            hoverOffset: 15,
            hoverBorderWidth: 0
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        cutout: '65%',
        layout: {
            padding: 20
        },
        plugins: {
            legend: {
                position: 'top',
                labels: {
                    padding: 20,
                    usePointStyle: true,
                    pointStyle: 'circle'
                }
            },
            tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                padding: 12,
                titleColor: '#fff',
                titleFont: {
                    size: 14,
                    weight: 'bold'
                },
                bodyFont: {
                    size: 13
                },
                callbacks: {
                    label: function(context) {
                        let label = context.dataset.label || '';
                        let value = context.raw;
                        return `${label}: ${value}`;
                    }
                }
            }
        }
    }
});
    }

    // Gráfico de Vendas por Hora
    const hourCtx = document.getElementById('salesByHourChart');
    if (hourCtx) {
        const ctx = hourCtx.getContext('2d');
        const hourGradientFill = ctx.createLinearGradient(0, 0, 0, 400);
        hourGradientFill.addColorStop(0, 'rgba(101, 113, 255, 0.3)');
        hourGradientFill.addColorStop(1, 'rgba(101, 113, 255, 0)');

        const hourGradientFill2 = ctx.createLinearGradient(0, 0, 0, 400);
        hourGradientFill2.addColorStop(0, 'rgba(14, 163, 109, 0.3)');
        hourGradientFill2.addColorStop(1, 'rgba(14, 163, 109, 0)');

        new Chart(ctx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode($hourLabels); ?>,
        datasets: [{
            label: 'Número de Vendas',
            data: <?php echo json_encode($hourSales); ?>,
            borderColor: '#6571ff',
            backgroundColor: hourGradientFill,
            tension: 0.4,
            fill: true,
            pointBackgroundColor: '#6571ff',
            pointBorderColor: '#fff',
            pointBorderWidth: 2,
            pointRadius: 4,
            pointHoverRadius: 6
        }, {
            label: 'Receita (R$)',
            data: <?php echo json_encode($hourRevenue); ?>,
            borderColor: '#0ea36d',
            backgroundColor: hourGradientFill2,
            tension: 0.4,
            fill: true,
            pointBackgroundColor: '#0ea36d',
            pointBorderColor: '#fff',
            pointBorderWidth: 2,
            pointRadius: 4,
            pointHoverRadius: 6,
            yAxisID: 'revenue'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
            intersect: false,
            mode: 'index'
        },
        plugins: {
            legend: {
                position: 'top',
                labels: {
                    usePointStyle: true,
                    pointStyle: 'circle',
                    padding: 20
                }
            },
            tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                padding: 12,
                titleColor: '#fff',
                titleFont: {
                    size: 14,
                    weight: 'bold'
                },
                bodyFont: {
                    size: 13
                },
                callbacks: {
                    label: function(context) {
                        let label = context.dataset.label || '';
                        let value = context.raw;
                        if (label.includes('Receita')) {
                            return `${label}: R$ ${value.toFixed(2)}`;
                        }
                        return `${label}: ${value}`;
                    }
                }
            }
        },
        scales: {
            x: {
                grid: {
                    display: false
                },
                ticks: {
                    maxRotation: 45,
                    minRotation: 45,
                    font: {
                        size: 11
                    }
                }
            },
            y: {
                beginAtZero: true,
                grid: {
                    borderDash: [8, 4]
                }
            },
            revenue: {
                position: 'right',
                beginAtZero: true,
                grid: {
                    display: false
                },
                ticks: {
                    callback: function(value) {
                        return 'R$ ' + value.toFixed(2);
                    }
                }
            }
        }
    }
});
    }

    // Gráfico de Status (Pizza)
    const statusPieCtx = document.getElementById('statusPieChart');
    if (statusPieCtx) {
        new Chart(statusPieCtx.getContext('2d'), {
    type: 'doughnut',
    data: {
        labels: <?php echo json_encode($statusLabels); ?>,
        datasets: [{
            data: <?php echo json_encode($statusData); ?>,
            backgroundColor: [
                '#28a745',  // approved
                '#ffc107',  // pending
                '#dc3545',  // cancelled
                '#17a2b8'   // refunded
            ],
            borderColor: '#ffffff',
            borderWidth: 2,
            hoverOffset: 15
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        cutout: '65%',
        plugins: {
            legend: {
                position: 'right',
                labels: {
                    padding: 20,
                    usePointStyle: true,
                    pointStyle: 'circle'
                }
            }
        }
    }
});
    }

    // Gráfico de Vendas por Status
    const salesStatusCtx = document.getElementById('salesByStatusChart');
    if (salesStatusCtx) {
        new Chart(salesStatusCtx.getContext('2d'), {
    type: 'bar',
    data: {
        labels: <?php echo json_encode($statusLabels); ?>,
        datasets: [{
            label: 'Número de Vendas',
            data: <?php echo json_encode($statusValues); ?>,
            backgroundColor: Object.values(<?php echo json_encode($statusColors); ?>),
            borderColor: Object.values(<?php echo json_encode($statusColors); ?>),
            borderWidth: 1
        }, {
            label: 'Valor Total (R$)',
            data: <?php echo json_encode($statusAmounts); ?>,
            backgroundColor: Object.values(<?php echo json_encode($statusColors); ?>).map(color => color + '80'),
            borderColor: Object.values(<?php echo json_encode($statusColors); ?>),
            borderWidth: 1,
            yAxisID: 'revenue'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
                labels: {
                    usePointStyle: true,
                    pointStyle: 'circle',
                    padding: 20
                }
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        let label = context.dataset.label || '';
                        let value = context.raw;
                        if (label.includes('Valor')) {
                            return `${label}: R$ ${value.toFixed(2)}`;
                        }
                        return `${label}: ${value}`;
                    }
                }
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    borderDash: [8, 4]
                }
            },
            revenue: {
                position: 'right',
                beginAtZero: true,
                grid: {
                    display: false
                },
                ticks: {
                    callback: function(value) {
                        return 'R$ ' + value.toFixed(2);
                    }
                }
            }
        }
    }
});
    }

    // Gráfico de Status dos Usuários
    const userStatusCtx = document.getElementById('userStatusChart');
    if (userStatusCtx) {
        new Chart(userStatusCtx.getContext('2d'), {
    type: 'doughnut',
    data: {
        labels: <?php echo json_encode($userStatusLabels); ?>,
        datasets: [{
            data: <?php echo json_encode($userStatusValues); ?>,
            backgroundColor: [
                '#28a745',  // active
                '#ffc107',  // pending
                '#dc3545'   // inactive
            ],
            borderColor: '#ffffff',
            borderWidth: 2,
            hoverOffset: 15
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        cutout: '65%',
        plugins: {
            legend: {
                position: 'right',
                labels: {
                    padding: 20,
                    usePointStyle: true,
                    pointStyle: 'circle'
                }
            }
        }
    }
});
    }

} // Fim da verificação do Chart.js
} else {
    console.error('Chart.js não foi carregado');
}
</script>

<?php require_once 'includes/footer.php'; ?>
