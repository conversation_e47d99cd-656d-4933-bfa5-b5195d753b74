<?php
/**
 * Sistema de Upload Seguro
 * Proteção contra vulnerabilidades de upload de arquivos
 */

class SecureUpload {
    
    // Tipos de arquivo permitidos por categoria
    private static $allowedTypes = [
        'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        'document' => ['pdf', 'doc', 'docx', 'txt'],
        'favicon' => ['ico', 'png'],
        'video' => ['mp4', 'avi', 'mov', 'wmv'],
        'audio' => ['mp3', 'wav', 'ogg']
    ];
    
    // Tamanhos máximos por categoria (em bytes)
    private static $maxSizes = [
        'image' => 5 * 1024 * 1024,      // 5MB
        'document' => 10 * 1024 * 1024,  // 10MB
        'favicon' => 1 * 1024 * 1024,    // 1MB
        'video' => 50 * 1024 * 1024,     // 50MB
        'audio' => 10 * 1024 * 1024      // 10MB
    ];
    
    // MIME types permitidos
    private static $allowedMimes = [
        'jpg' => ['image/jpeg', 'image/pjpeg'],
        'jpeg' => ['image/jpeg', 'image/pjpeg'],
        'png' => ['image/png'],
        'gif' => ['image/gif'],
        'webp' => ['image/webp'],
        'ico' => ['image/x-icon', 'image/vnd.microsoft.icon'],
        'pdf' => ['application/pdf'],
        'doc' => ['application/msword'],
        'docx' => ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
        'txt' => ['text/plain'],
        'mp4' => ['video/mp4'],
        'avi' => ['video/x-msvideo'],
        'mov' => ['video/quicktime'],
        'wmv' => ['video/x-ms-wmv'],
        'mp3' => ['audio/mpeg'],
        'wav' => ['audio/wav'],
        'ogg' => ['audio/ogg']
    ];
    
    /**
     * Validar arquivo de upload
     */
    public static function validateFile($file, $category = 'image') {
        // Verificar se o arquivo foi enviado corretamente
        if (!isset($file['tmp_name']) || $file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception(self::getUploadErrorMessage($file['error'] ?? UPLOAD_ERR_NO_FILE));
        }
        
        // Verificar se o arquivo existe
        if (!is_uploaded_file($file['tmp_name'])) {
            throw new Exception('Arquivo não foi enviado via POST');
        }
        
        // Verificar tamanho
        if ($file['size'] > self::$maxSizes[$category]) {
            $maxSizeMB = round(self::$maxSizes[$category] / 1024 / 1024, 1);
            throw new Exception("Arquivo muito grande. Tamanho máximo: {$maxSizeMB}MB");
        }
        
        // Verificar se o arquivo não está vazio
        if ($file['size'] === 0) {
            throw new Exception('Arquivo está vazio');
        }
        
        // Obter e validar extensão
        $ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($ext, self::$allowedTypes[$category])) {
            $allowed = implode(', ', self::$allowedTypes[$category]);
            throw new Exception("Tipo de arquivo não permitido. Permitidos: {$allowed}");
        }
        
        // Verificar MIME type
        if (!self::validateMimeType($file['tmp_name'], $ext)) {
            throw new Exception('Tipo MIME do arquivo não corresponde à extensão');
        }
        
        // Verificar se não é um arquivo executável disfarçado
        if (self::isExecutableFile($file['tmp_name'])) {
            throw new Exception('Arquivo executável detectado');
        }
        
        // Validações específicas por categoria
        switch ($category) {
            case 'image':
                self::validateImage($file['tmp_name']);
                break;
            case 'document':
                self::validateDocument($file['tmp_name'], $ext);
                break;
        }
        
        return true;
    }
    
    /**
     * Validar MIME type do arquivo
     */
    private static function validateMimeType($filePath, $extension) {
        if (!function_exists('finfo_open')) {
            return true; // Se finfo não estiver disponível, pular validação
        }
        
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $filePath);
        finfo_close($finfo);
        
        if (!isset(self::$allowedMimes[$extension])) {
            return false;
        }
        
        return in_array($mimeType, self::$allowedMimes[$extension]);
    }
    
    /**
     * Verificar se é um arquivo executável
     */
    private static function isExecutableFile($filePath) {
        // Ler os primeiros bytes do arquivo
        $handle = fopen($filePath, 'rb');
        $header = fread($handle, 10);
        fclose($handle);
        
        // Assinaturas de arquivos executáveis
        $executableSignatures = [
            "\x4D\x5A",                 // PE/EXE
            "\x7F\x45\x4C\x46",         // ELF
            "\xCA\xFE\xBA\xBE",         // Mach-O
            "\xFE\xED\xFA\xCE",         // Mach-O
            "\x50\x4B\x03\x04",         // ZIP (pode conter executáveis)
        ];
        
        foreach ($executableSignatures as $signature) {
            if (strpos($header, $signature) === 0) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Validar arquivo de imagem
     */
    private static function validateImage($filePath) {
        $imageInfo = @getimagesize($filePath);
        if ($imageInfo === false) {
            throw new Exception('Arquivo não é uma imagem válida');
        }
        
        // Verificar dimensões mínimas e máximas
        $width = $imageInfo[0];
        $height = $imageInfo[1];
        
        if ($width < 10 || $height < 10) {
            throw new Exception('Imagem muito pequena (mínimo 10x10 pixels)');
        }
        
        if ($width > 5000 || $height > 5000) {
            throw new Exception('Imagem muito grande (máximo 5000x5000 pixels)');
        }
        
        return true;
    }
    
    /**
     * Validar documento
     */
    private static function validateDocument($filePath, $extension) {
        // Validações específicas por tipo de documento
        switch ($extension) {
            case 'pdf':
                return self::validatePDF($filePath);
            case 'txt':
                return self::validateTextFile($filePath);
        }
        
        return true;
    }
    
    /**
     * Validar arquivo PDF
     */
    private static function validatePDF($filePath) {
        $handle = fopen($filePath, 'rb');
        $header = fread($handle, 5);
        fclose($handle);
        
        if ($header !== '%PDF-') {
            throw new Exception('Arquivo PDF inválido');
        }
        
        return true;
    }
    
    /**
     * Validar arquivo de texto
     */
    private static function validateTextFile($filePath) {
        // Verificar se o arquivo contém apenas texto válido
        $content = file_get_contents($filePath);
        
        if (!mb_check_encoding($content, 'UTF-8')) {
            throw new Exception('Arquivo de texto com codificação inválida');
        }
        
        return true;
    }
    
    /**
     * Fazer upload seguro do arquivo
     */
    public static function secureUpload($file, $uploadDir, $category = 'image', $customName = null) {
        // Validar arquivo
        self::validateFile($file, $category);
        
        // Criar diretório com permissões seguras
        if (!is_dir($uploadDir)) {
            if (!mkdir($uploadDir, 0755, true)) {
                throw new Exception('Erro ao criar diretório de upload');
            }
        }
        
        // Gerar nome seguro para o arquivo
        $ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        if ($customName) {
            $filename = self::sanitizeFilename($customName) . '.' . $ext;
        } else {
            $filename = bin2hex(random_bytes(16)) . '.' . $ext;
        }
        
        // Verificar se o arquivo já existe
        $filepath = $uploadDir . $filename;
        $counter = 1;
        while (file_exists($filepath)) {
            $name = pathinfo($filename, PATHINFO_FILENAME);
            $filepath = $uploadDir . $name . '_' . $counter . '.' . $ext;
            $counter++;
        }
        
        // Mover arquivo
        if (!move_uploaded_file($file['tmp_name'], $filepath)) {
            throw new Exception('Erro ao mover arquivo para destino');
        }
        
        // Definir permissões seguras
        chmod($filepath, 0644);
        
        // Criar arquivo .htaccess para proteção adicional
        self::createHtaccessProtection($uploadDir);
        
        return basename($filepath);
    }
    
    /**
     * Sanitizar nome do arquivo
     */
    private static function sanitizeFilename($filename) {
        // Remover caracteres perigosos
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $filename);
        
        // Limitar tamanho
        $filename = substr($filename, 0, 100);
        
        // Remover pontos consecutivos
        $filename = preg_replace('/\.+/', '.', $filename);
        
        return trim($filename, '._-');
    }
    
    /**
     * Criar proteção .htaccess no diretório de upload
     */
    private static function createHtaccessProtection($uploadDir) {
        $htaccessFile = $uploadDir . '.htaccess';
        
        if (!file_exists($htaccessFile)) {
            $content = "# Proteção de upload\n";
            $content .= "Options -Indexes\n";
            $content .= "Options -ExecCGI\n";
            $content .= "<FilesMatch \"\\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$\">\n";
            $content .= "    Require all denied\n";
            $content .= "</FilesMatch>\n";
            
            file_put_contents($htaccessFile, $content);
        }
    }
    
    /**
     * Obter mensagem de erro de upload
     */
    private static function getUploadErrorMessage($errorCode) {
        $errors = [
            UPLOAD_ERR_INI_SIZE => 'Arquivo muito grande (limite do servidor)',
            UPLOAD_ERR_FORM_SIZE => 'Arquivo muito grande (limite do formulário)',
            UPLOAD_ERR_PARTIAL => 'Upload incompleto',
            UPLOAD_ERR_NO_FILE => 'Nenhum arquivo enviado',
            UPLOAD_ERR_NO_TMP_DIR => 'Diretório temporário não encontrado',
            UPLOAD_ERR_CANT_WRITE => 'Erro ao escrever arquivo',
            UPLOAD_ERR_EXTENSION => 'Upload bloqueado por extensão'
        ];
        
        return $errors[$errorCode] ?? 'Erro desconhecido no upload';
    }
    
    /**
     * Deletar arquivo de forma segura
     */
    public static function secureDelete($filepath) {
        if (file_exists($filepath) && is_file($filepath)) {
            // Verificar se o arquivo está dentro de um diretório permitido
            $realPath = realpath($filepath);
            $allowedDirs = [
                realpath(__DIR__ . '/../uploads/'),
                realpath(__DIR__ . '/../admin/uploads/'),
                realpath(__DIR__ . '/../imagens/')
            ];
            
            $isAllowed = false;
            foreach ($allowedDirs as $allowedDir) {
                if ($allowedDir && strpos($realPath, $allowedDir) === 0) {
                    $isAllowed = true;
                    break;
                }
            }
            
            if ($isAllowed) {
                return unlink($filepath);
            } else {
                throw new Exception('Tentativa de deletar arquivo fora dos diretórios permitidos');
            }
        }
        
        return false;
    }
}
?>
