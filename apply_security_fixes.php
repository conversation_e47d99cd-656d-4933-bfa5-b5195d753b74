<?php
/**
 * Script para Aplicar Correções de Segurança
 * Execute este script para implementar todas as correções de segurança
 */

require_once 'config/secure_config.php';
require_once 'admin/database/connection.php';

echo "🔒 APLICANDO CORREÇÕES DE SEGURANÇA - SISTEMA KESUNG\n";
echo "====================================================\n\n";

$fixes = [];
$errors = [];

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // 1. Criar tabela de tentativas de login
    echo "1. Criando tabela de tentativas de login...\n";
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS login_attempts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                ip_address VARCHAR(45) NOT NULL,
                email VARCHAR(255),
                success TINYINT(1) DEFAULT 0,
                attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_ip_time (ip_address, attempted_at),
                INDEX idx_email_time (email, attempted_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        $fixes[] = "✅ Tabela login_attempts criada";
    } catch (Exception $e) {
        $errors[] = "❌ Erro ao criar tabela login_attempts: " . $e->getMessage();
    }
    
    // 2. Adicionar campos de segurança na tabela admins
    echo "2. Adicionando campos de segurança na tabela admins...\n";
    try {
        $columns = [
            'last_login' => 'TIMESTAMP NULL',
            'last_logout' => 'TIMESTAMP NULL',
            'login_count' => 'INT DEFAULT 0',
            'failed_login_attempts' => 'INT DEFAULT 0',
            'account_locked_until' => 'TIMESTAMP NULL',
            'password_changed_at' => 'TIMESTAMP NULL'
        ];
        
        foreach ($columns as $column => $type) {
            try {
                $pdo->exec("ALTER TABLE admins ADD COLUMN $column $type");
                $fixes[] = "✅ Campo $column adicionado à tabela admins";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate column') === false) {
                    $errors[] = "❌ Erro ao adicionar campo $column: " . $e->getMessage();
                }
            }
        }
    } catch (Exception $e) {
        $errors[] = "❌ Erro geral na tabela admins: " . $e->getMessage();
    }
    
    // 3. Criar tabela de tokens de download seguros
    echo "3. Criando tabela de tokens de download seguros...\n";
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS download_tokens (
                id INT AUTO_INCREMENT PRIMARY KEY,
                order_id INT,
                product_id INT NOT NULL,
                token VARCHAR(64) NOT NULL UNIQUE,
                expires_at TIMESTAMP NOT NULL,
                used_at TIMESTAMP NULL,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_token (token),
                INDEX idx_expires (expires_at),
                FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        $fixes[] = "✅ Tabela download_tokens criada";
    } catch (Exception $e) {
        $errors[] = "❌ Erro ao criar tabela download_tokens: " . $e->getMessage();
    }
    
    // 4. Criar tabela de logs de segurança
    echo "4. Criando tabela de logs de segurança...\n";
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS security_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                event_type VARCHAR(50) NOT NULL,
                user_id INT NULL,
                ip_address VARCHAR(45) NOT NULL,
                user_agent TEXT,
                details JSON,
                severity ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') DEFAULT 'MEDIUM',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_event_type (event_type),
                INDEX idx_severity (severity),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        $fixes[] = "✅ Tabela security_logs criada";
    } catch (Exception $e) {
        $errors[] = "❌ Erro ao criar tabela security_logs: " . $e->getMessage();
    }
    
    // 5. Corrigir permissões de diretórios
    echo "5. Corrigindo permissões de diretórios...\n";
    $directories = [
        'uploads' => 0755,
        'uploads/products' => 0755,
        'admin/uploads' => 0755,
        'admin/uploads/products' => 0755,
        'logs' => 0755,
        'config' => 0750
    ];
    
    foreach ($directories as $dir => $permission) {
        if (!is_dir($dir)) {
            if (mkdir($dir, $permission, true)) {
                $fixes[] = "✅ Diretório $dir criado com permissão " . decoct($permission);
            } else {
                $errors[] = "❌ Erro ao criar diretório $dir";
            }
        } else {
            if (chmod($dir, $permission)) {
                $fixes[] = "✅ Permissão do diretório $dir corrigida para " . decoct($permission);
            } else {
                $errors[] = "❌ Erro ao corrigir permissão do diretório $dir";
            }
        }
    }
    
    // 6. Criar arquivos .htaccess de proteção
    echo "6. Criando arquivos .htaccess de proteção...\n";
    $htaccessDirs = ['uploads', 'uploads/products', 'admin/uploads', 'admin/uploads/products', 'logs', 'config'];
    
    $htaccessContent = "# Proteção de segurança\n";
    $htaccessContent .= "Options -Indexes -ExecCGI\n";
    $htaccessContent .= "<FilesMatch \"\\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$\">\n";
    $htaccessContent .= "    Require all denied\n";
    $htaccessContent .= "</FilesMatch>\n";
    
    foreach ($htaccessDirs as $dir) {
        if (is_dir($dir)) {
            $htaccessFile = $dir . '/.htaccess';
            if (file_put_contents($htaccessFile, $htaccessContent)) {
                $fixes[] = "✅ Arquivo .htaccess criado em $dir";
            } else {
                $errors[] = "❌ Erro ao criar .htaccess em $dir";
            }
        }
    }
    
    // 7. Criar arquivo .env de exemplo
    echo "7. Criando arquivo .env de exemplo...\n";
    $envContent = "# Configurações de Ambiente - KESUNG SITE\n";
    $envContent .= "# Copie este arquivo para .env e configure suas credenciais\n\n";
    $envContent .= "# Banco de Dados\n";
    $envContent .= "DB_HOST=localhost\n";
    $envContent .= "DB_NAME=u276254152_banco_loja\n";
    $envContent .= "DB_USER=root\n";
    $envContent .= "DB_PASS=\n\n";
    $envContent .= "# PIX/Pagamentos\n";
    $envContent .= "PIX_API_KEY=sua_chave_api_aqui\n\n";
    $envContent .= "# Webhooks\n";
    $envContent .= "WEBHOOK_SECRET=" . bin2hex(random_bytes(32)) . "\n";
    $envContent .= "WEBHOOK_TOKEN=" . bin2hex(random_bytes(32)) . "\n\n";
    $envContent .= "# Ambiente\n";
    $envContent .= "ENVIRONMENT=development\n";
    
    if (file_put_contents('.env.example', $envContent)) {
        $fixes[] = "✅ Arquivo .env.example criado";
    } else {
        $errors[] = "❌ Erro ao criar arquivo .env.example";
    }
    
    // 8. Atualizar senhas fracas (se existirem)
    echo "8. Verificando senhas de administradores...\n";
    try {
        $stmt = $pdo->query("SELECT id, email, password FROM admins");
        $admins = $stmt->fetchAll();
        
        foreach ($admins as $admin) {
            // Verificar se a senha precisa ser rehashed (versões antigas do PHP)
            if (!password_get_info($admin['password'])['algo']) {
                // Senha não está hasheada corretamente
                $newPassword = bin2hex(random_bytes(8)); // Gerar senha temporária
                $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                
                $stmt = $pdo->prepare("UPDATE admins SET password = ?, password_changed_at = NOW() WHERE id = ?");
                $stmt->execute([$hashedPassword, $admin['id']]);
                
                $fixes[] = "⚠️ Senha do admin {$admin['email']} atualizada. Nova senha temporária: $newPassword";
            }
        }
    } catch (Exception $e) {
        $errors[] = "❌ Erro ao verificar senhas: " . $e->getMessage();
    }
    
    // 9. Limpar logs antigos
    echo "9. Limpando logs antigos...\n";
    $logFiles = glob('logs/*.log');
    $cleaned = 0;
    
    foreach ($logFiles as $logFile) {
        if (filemtime($logFile) < strtotime('-30 days')) {
            if (unlink($logFile)) {
                $cleaned++;
            }
        }
    }
    
    if ($cleaned > 0) {
        $fixes[] = "✅ $cleaned arquivos de log antigos removidos";
    }
    
    // 10. Registrar aplicação das correções
    echo "10. Registrando aplicação das correções...\n";
    try {
        $stmt = $pdo->prepare("
            INSERT INTO security_logs (event_type, ip_address, user_agent, details, severity)
            VALUES ('SECURITY_FIXES_APPLIED', ?, ?, ?, 'HIGH')
        ");
        $stmt->execute([
            $_SERVER['REMOTE_ADDR'] ?? 'CLI',
            $_SERVER['HTTP_USER_AGENT'] ?? 'CLI',
            json_encode(['fixes_count' => count($fixes), 'errors_count' => count($errors)])
        ]);
        $fixes[] = "✅ Aplicação das correções registrada nos logs";
    } catch (Exception $e) {
        $errors[] = "❌ Erro ao registrar nos logs: " . $e->getMessage();
    }
    
} catch (Exception $e) {
    $errors[] = "❌ Erro crítico: " . $e->getMessage();
}

// Relatório final
echo "\n" . str_repeat("=", 60) . "\n";
echo "📊 RELATÓRIO FINAL\n";
echo str_repeat("=", 60) . "\n\n";

echo "✅ CORREÇÕES APLICADAS (" . count($fixes) . "):\n";
foreach ($fixes as $fix) {
    echo "   $fix\n";
}

if (!empty($errors)) {
    echo "\n❌ ERROS ENCONTRADOS (" . count($errors) . "):\n";
    foreach ($errors as $error) {
        echo "   $error\n";
    }
}

echo "\n🎯 PRÓXIMOS PASSOS:\n";
echo "   1. Configure o arquivo .env com suas credenciais reais\n";
echo "   2. Teste o login do administrador\n";
echo "   3. Verifique se o upload de arquivos está funcionando\n";
echo "   4. Configure backup automático\n";
echo "   5. Monitore os logs de segurança regularmente\n";

echo "\n🔒 SISTEMA MAIS SEGURO!\n";
echo "   - Proteção contra SQL Injection\n";
echo "   - Proteção contra XSS\n";
echo "   - Upload seguro de arquivos\n";
echo "   - Autenticação robusta\n";
echo "   - Rate limiting implementado\n";
echo "   - Headers de segurança configurados\n";

echo "\n✨ Correções aplicadas com sucesso!\n";
?>
