/* ========== CSS ESPECÍFICO PARA DASHBOARD ========== */
/* Estilos específicos para a página de dashboard */

/* ========== CARDS DO DASHBOARD ========== */

.dashboard-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.dashboard-card {
    background: white;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 20px;
    height: 120px;
    overflow: hidden;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dashboard-card .card-body {
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
}

/* Ícones dos Cards - CENTRALIZADOS */
.card-icon {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.sales-icon { 
    background: rgba(25, 135, 84, 0.1); 
    color: #198754; 
}

.revenue-icon { 
    background: rgba(40, 167, 69, 0.1); 
    color: #28a745; 
}

.products-icon { 
    background: rgba(255, 193, 7, 0.1); 
    color: #ffc107; 
}

.customers-icon { 
    background: rgba(220, 53, 69, 0.1); 
    color: #dc3545; 
}

/* Conteúdo dos cards */
.card-content {
    text-align: right;
    flex: 1;
    margin-left: 16px;
}

.card-content h3 {
    margin-bottom: 4px;
    line-height: 1.1;
    font-size: 24px;
    font-weight: 700;
    color: #212529;
}

.card-content p {
    margin-bottom: 0;
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
}

/* ========== TABELAS DO DASHBOARD ========== */

.dashboard-table-card {
    background: white;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    overflow: hidden;
}

.dashboard-table-card .card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e2e8f0;
    padding: 16px 20px;
    font-size: 16px;
    font-weight: 600;
    color: #212529;
}

.dashboard-table-card .card-body {
    padding: 0;
}

/* Container da tabela sem barra de rolagem */
.table-container {
    width: 100%;
    max-width: 100%;
    overflow: hidden;
}

/* Tabela responsiva sem scroll horizontal */
.table-responsive-dashboard {
    width: 100%;
    max-width: 100%;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
}

/* Tabela com largura total e responsiva */
.table-dashboard {
    width: 100%;
    margin: 0;
    border-collapse: collapse;
    table-layout: fixed;
    font-size: 14px;
}

/* Cabeçalho da tabela */
.table-dashboard th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #e2e8f0;
    font-weight: 600;
    color: #495057;
    padding: 12px 8px;
    vertical-align: middle;
    white-space: nowrap;
    font-size: 13px;
    height: 45px;
    line-height: 1.2;
}

/* Células da tabela */
.table-dashboard td {
    padding: 12px 8px;
    vertical-align: middle;
    border-bottom: 1px solid #e2e8f0;
    font-size: 12px;
    height: 50px;
    line-height: 1.3;
}

/* Larguras específicas das colunas */
.table-dashboard th:nth-child(1), .table-dashboard td:nth-child(1) {
    width: 8%;
    text-align: center;
}

.table-dashboard th:nth-child(2), .table-dashboard td:nth-child(2) {
    width: 25%;
    text-align: left;
}

.table-dashboard th:nth-child(3), .table-dashboard td:nth-child(3) {
    width: 20%;
    text-align: left;
}

.table-dashboard th:nth-child(4), .table-dashboard td:nth-child(4) {
    width: 12%;
    text-align: right;
    padding-right: 12px;
}

.table-dashboard th:nth-child(5), .table-dashboard td:nth-child(5) {
    width: 12%;
    text-align: center;
}

.table-dashboard th:nth-child(6), .table-dashboard td:nth-child(6) {
    width: 8%;
    text-align: center;
}

.table-dashboard th:nth-child(7), .table-dashboard td:nth-child(7) {
    width: 10%;
    text-align: center;
}

.table-dashboard th:nth-child(8), .table-dashboard td:nth-child(8) {
    width: 5%;
    text-align: center;
}

/* Badges na tabela */
.table-dashboard .badge {
    font-size: 11px;
    padding: 4px 8px;
    font-weight: 500;
    border-radius: 6px;
    min-width: 70px;
    text-align: center;
    display: inline-block;
    line-height: 1.2;
}

/* Botões de ação na tabela */
.table-dashboard .btn-action {
    padding: 4px 8px;
    font-size: 11px;
    border-radius: 6px;
    margin: 0 1px;
    min-width: 30px;
    height: 28px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Hover effect para linhas */
.table-dashboard tbody tr:hover {
    background-color: rgba(25, 135, 84, 0.05);
}

/* Valores monetários */
.table-dashboard .text-success {
    font-weight: 600;
    color: #28a745;
}

/* ========== RESPONSIVIDADE ========== */

@media (max-width: 1199.98px) {
    .dashboard-stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 16px;
    }
    
    .dashboard-card {
        height: 100px;
    }
    
    .card-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
    
    .card-content h3 {
        font-size: 20px;
    }
    
    .table-dashboard {
        font-size: 12px;
    }
    
    .table-dashboard th,
    .table-dashboard td {
        padding: 8px 6px;
    }
}

@media (max-width: 991.98px) {
    .dashboard-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }
    
    .dashboard-card {
        height: 90px;
    }
    
    .dashboard-card .card-body {
        padding: 12px;
    }
    
    .card-icon {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }
    
    .card-content h3 {
        font-size: 18px;
    }
    
    .card-content p {
        font-size: 12px;
    }
    
    .table-dashboard {
        font-size: 11px;
    }
    
    .table-dashboard th,
    .table-dashboard td {
        padding: 6px 4px;
    }
    
    .table-dashboard .btn-action {
        height: 24px;
        min-width: 24px;
        padding: 2px 4px;
        font-size: 10px;
    }
    
    .table-dashboard .badge {
        font-size: 10px;
        padding: 2px 6px;
        min-width: 50px;
    }
}

@media (max-width: 767.98px) {
    .dashboard-stats-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .dashboard-card {
        height: 80px;
    }
    
    .dashboard-card .card-body {
        padding: 10px;
    }
    
    .card-icon {
        width: 30px;
        height: 30px;
        font-size: 12px;
    }
    
    .card-content h3 {
        font-size: 16px;
    }
    
    .card-content p {
        font-size: 11px;
    }
    
    /* Ocultar colunas menos importantes em mobile */
    .table-dashboard th:nth-child(3), 
    .table-dashboard td:nth-child(3),
    .table-dashboard th:nth-child(6), 
    .table-dashboard td:nth-child(6),
    .table-dashboard th:nth-child(7), 
    .table-dashboard td:nth-child(7) {
        display: none;
    }
    
    /* Reajustar larguras das colunas restantes */
    .table-dashboard th:nth-child(1), .table-dashboard td:nth-child(1) { width: 10%; }
    .table-dashboard th:nth-child(2), .table-dashboard td:nth-child(2) { width: 40%; }
    .table-dashboard th:nth-child(4), .table-dashboard td:nth-child(4) { width: 20%; }
    .table-dashboard th:nth-child(5), .table-dashboard td:nth-child(5) { width: 20%; }
    .table-dashboard th:nth-child(8), .table-dashboard td:nth-child(8) { width: 10%; }
}

/* ========== FIM DO CSS DASHBOARD ========== */
