<?php
/**
 * Sistema de Autenticação Segura
 * Proteção contra ataques de força bruta, session hijacking e outras vulnerabilidades
 */

require_once __DIR__ . '/../config/secure_config.php';

class SecureAuth {
    
    private static $sessionTimeout = 3600; // 1 hora
    private static $maxLoginAttempts = 5;
    private static $lockoutTime = 900; // 15 minutos
    
    /**
     * Iniciar sessão segura
     */
    public static function startSecureSession() {
        // Configurações seguras de sessão
        if (session_status() === PHP_SESSION_NONE) {
            // Configurar parâmetros de sessão antes de iniciar
            ini_set('session.cookie_httponly', 1);
            ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
            ini_set('session.use_strict_mode', 1);
            ini_set('session.cookie_samesite', 'Strict');
            ini_set('session.gc_maxlifetime', self::$sessionTimeout);
            
            // Nome da sessão personalizado
            session_name('KESUNG_SECURE_SESSION');
            
            session_start();
        }
        
        // Verificar timeout de sessão
        if (isset($_SESSION['last_activity'])) {
            if (time() - $_SESSION['last_activity'] > self::$sessionTimeout) {
                self::destroySession();
                return false;
            }
        }
        
        $_SESSION['last_activity'] = time();
        
        // Regenerar ID da sessão periodicamente
        if (!isset($_SESSION['created'])) {
            $_SESSION['created'] = time();
        } else if (time() - $_SESSION['created'] > 300) { // 5 minutos
            session_regenerate_id(true);
            $_SESSION['created'] = time();
        }
        
        // Verificar integridade da sessão
        if (!self::validateSessionIntegrity()) {
            self::destroySession();
            return false;
        }
        
        return true;
    }
    
    /**
     * Validar integridade da sessão
     */
    private static function validateSessionIntegrity() {
        // Verificar IP (opcional - pode causar problemas com proxies)
        if (isset($_SESSION['ip_address'])) {
            if ($_SESSION['ip_address'] !== self::getClientIP()) {
                error_log("Session hijacking attempt detected. Session IP: {$_SESSION['ip_address']}, Current IP: " . self::getClientIP());
                return false;
            }
        } else {
            $_SESSION['ip_address'] = self::getClientIP();
        }
        
        // Verificar User-Agent
        if (isset($_SESSION['user_agent'])) {
            if ($_SESSION['user_agent'] !== ($_SERVER['HTTP_USER_AGENT'] ?? '')) {
                error_log("Session hijacking attempt detected. Different User-Agent.");
                return false;
            }
        } else {
            $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? '';
        }
        
        return true;
    }
    
    /**
     * Obter IP real do cliente
     */
    private static function getClientIP() {
        $ipKeys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * Verificar autenticação
     */
    public static function checkAuth() {
        if (!self::startSecureSession()) {
            return false;
        }
        
        if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
            return false;
        }
        
        // Verificar se o usuário ainda está ativo no banco
        if (isset($_SESSION['admin_id'])) {
            try {
                $db = Database::getInstance();
                $pdo = $db->getConnection();
                
                $stmt = $pdo->prepare("SELECT active FROM admins WHERE id = ?");
                $stmt->execute([$_SESSION['admin_id']]);
                $admin = $stmt->fetch();
                
                if (!$admin || !$admin['active']) {
                    self::destroySession();
                    return false;
                }
                
                // Atualizar último acesso
                $stmt = $pdo->prepare("UPDATE admins SET last_login = NOW() WHERE id = ?");
                $stmt->execute([$_SESSION['admin_id']]);
                
            } catch (Exception $e) {
                error_log("Error checking admin status: " . $e->getMessage());
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Verificar rate limiting de login
     */
    private static function checkRateLimit($ip, $email = null) {
        try {
            $db = Database::getInstance();
            $pdo = $db->getConnection();
            
            // Criar tabela se não existir
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS login_attempts (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    ip_address VARCHAR(45) NOT NULL,
                    email VARCHAR(255),
                    success TINYINT(1) DEFAULT 0,
                    attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_ip_time (ip_address, attempted_at),
                    INDEX idx_email_time (email, attempted_at)
                )
            ");
            
            // Verificar tentativas por IP
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as attempts 
                FROM login_attempts 
                WHERE ip_address = ? 
                AND success = 0 
                AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)
            ");
            $stmt->execute([$ip, self::$lockoutTime]);
            $ipAttempts = $stmt->fetchColumn();
            
            if ($ipAttempts >= self::$maxLoginAttempts) {
                throw new Exception("Muitas tentativas de login deste IP. Tente novamente em " . (self::$lockoutTime / 60) . " minutos.");
            }
            
            // Verificar tentativas por email se fornecido
            if ($email) {
                $stmt = $pdo->prepare("
                    SELECT COUNT(*) as attempts 
                    FROM login_attempts 
                    WHERE email = ? 
                    AND success = 0 
                    AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)
                ");
                $stmt->execute([$email, self::$lockoutTime]);
                $emailAttempts = $stmt->fetchColumn();
                
                if ($emailAttempts >= self::$maxLoginAttempts) {
                    throw new Exception("Muitas tentativas de login para este email. Tente novamente em " . (self::$lockoutTime / 60) . " minutos.");
                }
            }
            
            return true;
            
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Muitas tentativas') === 0) {
                throw $e;
            }
            error_log("Error checking rate limit: " . $e->getMessage());
            return true; // Em caso de erro, permitir tentativa
        }
    }
    
    /**
     * Registrar tentativa de login
     */
    private static function logLoginAttempt($ip, $email, $success) {
        try {
            $db = Database::getInstance();
            $pdo = $db->getConnection();
            
            $stmt = $pdo->prepare("
                INSERT INTO login_attempts (ip_address, email, success, attempted_at) 
                VALUES (?, ?, ?, NOW())
            ");
            $stmt->execute([$ip, $email, $success ? 1 : 0]);
            
            // Limpar tentativas antigas (mais de 24 horas)
            $pdo->exec("DELETE FROM login_attempts WHERE attempted_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)");
            
        } catch (Exception $e) {
            error_log("Error logging login attempt: " . $e->getMessage());
        }
    }
    
    /**
     * Fazer login seguro
     */
    public static function login($email, $password) {
        $ip = self::getClientIP();
        
        try {
            // Validar entrada
            if (empty($email) || empty($password)) {
                throw new Exception('Email e senha são obrigatórios');
            }
            
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Email inválido');
            }
            
            // Verificar rate limiting
            self::checkRateLimit($ip, $email);
            
            $db = Database::getInstance();
            $pdo = $db->getConnection();
            
            // Buscar admin
            $stmt = $pdo->prepare("SELECT * FROM admins WHERE email = ? AND active = 1");
            $stmt->execute([$email]);
            $admin = $stmt->fetch();
            
            if ($admin && password_verify($password, $admin['password'])) {
                // Login bem-sucedido
                self::logLoginAttempt($ip, $email, true);
                
                // Iniciar sessão segura
                self::startSecureSession();
                
                // Regenerar ID da sessão por segurança
                session_regenerate_id(true);
                
                // Definir variáveis de sessão
                $_SESSION['admin_logged_in'] = true;
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['admin_email'] = $admin['email'];
                $_SESSION['admin_name'] = $admin['name'];
                $_SESSION['login_time'] = time();
                $_SESSION['ip_address'] = $ip;
                $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? '';
                
                // Atualizar último login no banco
                $stmt = $pdo->prepare("UPDATE admins SET last_login = NOW(), login_count = login_count + 1 WHERE id = ?");
                $stmt->execute([$admin['id']]);
                
                return true;
                
            } else {
                // Login falhou
                self::logLoginAttempt($ip, $email, false);
                
                // Delay para dificultar ataques de força bruta
                usleep(rand(500000, 1500000)); // 0.5 a 1.5 segundos
                
                throw new Exception('Email ou senha incorretos');
            }
            
        } catch (Exception $e) {
            error_log("Login error for $email from $ip: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Fazer logout seguro
     */
    public static function logout() {
        if (session_status() === PHP_SESSION_ACTIVE) {
            // Registrar logout
            if (isset($_SESSION['admin_id'])) {
                try {
                    $db = Database::getInstance();
                    $pdo = $db->getConnection();
                    
                    $stmt = $pdo->prepare("UPDATE admins SET last_logout = NOW() WHERE id = ?");
                    $stmt->execute([$_SESSION['admin_id']]);
                } catch (Exception $e) {
                    error_log("Error logging logout: " . $e->getMessage());
                }
            }
            
            self::destroySession();
        }
    }
    
    /**
     * Destruir sessão de forma segura
     */
    public static function destroySession() {
        if (session_status() === PHP_SESSION_ACTIVE) {
            // Limpar todas as variáveis de sessão
            $_SESSION = array();
            
            // Deletar cookie de sessão
            if (ini_get("session.use_cookies")) {
                $params = session_get_cookie_params();
                setcookie(session_name(), '', time() - 42000,
                    $params["path"], $params["domain"],
                    $params["secure"], $params["httponly"]
                );
            }
            
            // Destruir sessão
            session_destroy();
        }
    }
    
    /**
     * Gerar token CSRF
     */
    public static function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time']) || 
            (time() - $_SESSION['csrf_token_time']) > 1800) { // 30 minutos
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
            $_SESSION['csrf_token_time'] = time();
        }
        
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Validar token CSRF
     */
    public static function validateCSRFToken($token) {
        if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
            return false;
        }
        
        // Verificar se o token não expirou
        if ((time() - $_SESSION['csrf_token_time']) > 1800) { // 30 minutos
            return false;
        }
        
        // Comparação segura contra timing attacks
        return hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * Verificar se o usuário tem permissão específica
     */
    public static function hasPermission($permission) {
        if (!self::checkAuth()) {
            return false;
        }
        
        try {
            $db = Database::getInstance();
            $pdo = $db->getConnection();
            
            $stmt = $pdo->prepare("
                SELECT COUNT(*) 
                FROM admin_permissions ap 
                JOIN permissions p ON ap.permission_id = p.id 
                WHERE ap.admin_id = ? AND p.name = ?
            ");
            $stmt->execute([$_SESSION['admin_id'], $permission]);
            
            return $stmt->fetchColumn() > 0;
            
        } catch (Exception $e) {
            error_log("Error checking permission: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Obter informações do usuário logado
     */
    public static function getCurrentUser() {
        if (!self::checkAuth()) {
            return null;
        }
        
        return [
            'id' => $_SESSION['admin_id'],
            'email' => $_SESSION['admin_email'],
            'name' => $_SESSION['admin_name'],
            'login_time' => $_SESSION['login_time'],
            'last_activity' => $_SESSION['last_activity']
        ];
    }
}
?>
