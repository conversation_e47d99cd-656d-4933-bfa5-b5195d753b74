/* ========== CSS BASE PARA ADMIN SYSTEM ========== */
/* Este arquivo trabalha em conjunto com standardized.css */

:root {
    --primary-color: #198754;
    --primary-hover: #157347;
    --secondary-color: #6c757d;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --white-color: #ffffff;
    --border-color: #e2e8f0;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
    --shadow-lg: 0 8px 15px rgba(0,0,0,0.1);
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    min-height: 100vh;
    background-color: var(--light-color);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
}

/* ========== LAYOUT ESPECÍFICO ========== */
/* Estilos específicos que complementam o standardized.css */

/* Container principal */
.container-fluid {
    padding: 20px;
    max-width: 100%;
}

/* Títulos de página */
.page-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.page-title h1, .page-title h2 {
    margin: 0;
    font-size: inherit;
    font-weight: inherit;
}

/* Breadcrumbs */
.breadcrumb {
    background: transparent;
    padding: 0;
    margin-bottom: 20px;
}

.breadcrumb-item + .breadcrumb-item::before {
    color: var(--secondary-color);
}

/* Stats cards específicos */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    border-radius: var(--border-radius-lg);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow-md);
}

.stats-card .stats-number {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 5px;
}

.stats-card .stats-label {
    font-size: 14px;
    opacity: 0.9;
}

/* ========== COMPONENTES ESPECÍFICOS ========== */

/* Loading states */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--light-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notificações */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 16px 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    max-width: 400px;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Tooltips customizados */
.tooltip-custom {
    position: relative;
    cursor: help;
}

.tooltip-custom::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--dark-color);
    color: white;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
}

.tooltip-custom:hover::after {
    opacity: 1;
    visibility: visible;
}

/* Dropzone para upload */
.dropzone {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 40px;
    text-align: center;
    background: var(--light-color);
    transition: var(--transition);
    cursor: pointer;
}

.dropzone:hover {
    border-color: var(--primary-color);
    background: rgba(25, 135, 84, 0.05);
}

.dropzone.dragover {
    border-color: var(--primary-color);
    background: rgba(25, 135, 84, 0.1);
}

/* Progress bars */
.progress-custom {
    height: 8px;
    background: var(--light-color);
    border-radius: 4px;
    overflow: hidden;
}

.progress-custom .progress-bar {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

/* ========== ESTILOS ESPECÍFICOS PARA PÁGINAS ========== */

/* Dashboard específico */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* Formulários específicos */
.form-section {
    background: var(--white-color);
    border-radius: var(--border-radius-lg);
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.form-section-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
}

/* Tabelas específicas */
.table-actions {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: center;
}

.table-status {
    text-align: center;
}

.table-currency {
    text-align: right;
    font-weight: 600;
    color: var(--success-color);
}

/* Filtros e busca */
.filters-section {
    background: var(--white-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.search-box {
    position: relative;
    max-width: 300px;
}

.search-box input {
    padding-left: 40px;
}

.search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--secondary-color);
}

/* ========== RESPONSIVIDADE ESPECÍFICA ========== */

@media (max-width: 768px) {
    .dashboard-stats {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .form-section {
        padding: 16px;
        margin-bottom: 16px;
    }

    .filters-section {
        padding: 16px;
    }

    .search-box {
        max-width: 100%;
    }

    .table-actions {
        flex-direction: column;
        gap: 4px;
    }
}

/* ========== FIM DOS ESTILOS ESPECÍFICOS ========== */
