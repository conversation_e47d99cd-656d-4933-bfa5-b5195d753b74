/* ========== CSS PADRONIZADO ADMIN SYSTEM ========== */
/* Sistema de Vendas - Admin Panel - CSS Unificado e Padronizado */

/* ========== VARIÁVEIS CSS ========== */
:root {
    --primary-color: #198754;
    --primary-hover: #157347;
    --secondary-color: #6c757d;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --white-color: #ffffff;
    --border-color: #e2e8f0;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
    --shadow-lg: 0 8px 15px rgba(0,0,0,0.1);
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --transition: all 0.3s ease;
}

/* ========== RESET E BASE ========== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    min-height: 100vh;
    background-color: var(--light-color);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
}

/* ========== BOTÕES PADRONIZADOS ========== */

/* Botões Padrão */
.btn {
    height: 40px !important;
    min-width: 100px !important;
    padding: 8px 16px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    border-radius: var(--border-radius) !important;
    border: none !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-decoration: none !important;
    transition: var(--transition) !important;
    white-space: nowrap !important;
    line-height: 1.2 !important;
    cursor: pointer !important;
}

/* Botões Pequenos */
.btn-sm {
    height: 32px !important;
    min-width: 80px !important;
    padding: 6px 12px !important;
    font-size: 12px !important;
}

.btn-sm i {
    font-size: 12px !important;
}

/* Botões Grandes */
.btn-lg {
    height: 48px !important;
    min-width: 140px !important;
    padding: 12px 24px !important;
    font-size: 16px !important;
}

/* Botões de Ação em Tabelas */
.btn-action {
    height: 28px !important;
    min-width: 60px !important;
    padding: 4px 8px !important;
    font-size: 11px !important;
    margin: 1px !important;
}

.btn-action i {
    font-size: 11px !important;
}

/* ========== CORES DOS BOTÕES ========== */
.btn-primary {
    background: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
}
.btn-primary:hover {
    background: var(--primary-hover) !important;
    border-color: var(--primary-hover) !important;
}

.btn-success {
    background: var(--success-color) !important;
    border-color: var(--success-color) !important;
    color: white !important;
}
.btn-success:hover {
    background: #218838 !important;
    border-color: #1e7e34 !important;
}

.btn-danger {
    background: var(--danger-color) !important;
    border-color: var(--danger-color) !important;
    color: white !important;
}
.btn-danger:hover {
    background: #c82333 !important;
    border-color: #bd2130 !important;
}

.btn-warning {
    background: var(--warning-color) !important;
    border-color: var(--warning-color) !important;
    color: #212529 !important;
}
.btn-warning:hover {
    background: #e0a800 !important;
    border-color: #d39e00 !important;
}

.btn-info {
    background: var(--info-color) !important;
    border-color: var(--info-color) !important;
    color: white !important;
}
.btn-info:hover {
    background: #138496 !important;
    border-color: #117a8b !important;
}

.btn-secondary {
    background: var(--secondary-color) !important;
    border-color: var(--secondary-color) !important;
    color: white !important;
}
.btn-secondary:hover {
    background: #5a6268 !important;
    border-color: #545b62 !important;
}

/* Botões Outline */
.btn-outline-primary { border-color: var(--primary-color) !important; color: var(--primary-color) !important; }
.btn-outline-success { border-color: var(--success-color) !important; color: var(--success-color) !important; }
.btn-outline-danger { border-color: var(--danger-color) !important; color: var(--danger-color) !important; }
.btn-outline-warning { border-color: var(--warning-color) !important; color: var(--warning-color) !important; }
.btn-outline-info { border-color: var(--info-color) !important; color: var(--info-color) !important; }
.btn-outline-secondary { border-color: var(--secondary-color) !important; color: var(--secondary-color) !important; }

/* ========== CARDS PADRONIZADOS ========== */

.card, .dashboard-card {
    border-radius: var(--border-radius-lg) !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: var(--shadow-md) !important;
    margin-bottom: 20px !important;
    background: var(--white-color) !important;
    transition: var(--transition) !important;
}

.card:hover {
    box-shadow: var(--shadow-lg) !important;
    transform: translateY(-1px) !important;
}

.card-header {
    height: 60px !important;
    padding: 16px 20px !important;
    background: var(--light-color) !important;
    border-bottom: 1px solid var(--border-color) !important;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
}

.card-body {
    padding: 20px !important;
}

.card-title {
    font-size: 16px !important;
    font-weight: 600 !important;
    margin: 0 !important;
    color: var(--dark-color) !important;
}

/* ========== DASHBOARD CARDS ESPECÍFICOS ========== */

.dashboard-card .card-body {
    height: 120px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 16px !important;
}

.dashboard-card .card-icon {
    width: 50px !important;
    height: 50px !important;
    border-radius: var(--border-radius) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 20px !important;
    flex-shrink: 0 !important;
}

.dashboard-card .card-content {
    flex: 1 !important;
    text-align: right !important;
    margin-left: 16px !important;
}

.dashboard-card .number {
    font-size: 24px !important;
    font-weight: 700 !important;
    line-height: 1 !important;
    margin: 0 0 4px 0 !important;
    color: var(--dark-color) !important;
}

.dashboard-card .label {
    font-size: 14px !important;
    color: var(--secondary-color) !important;
    margin: 0 !important;
    font-weight: 500 !important;
}

/* Cores dos ícones dos cards */
.sales-icon { background: rgba(25, 135, 84, 0.1) !important; color: var(--primary-color) !important; }
.revenue-icon { background: rgba(40, 167, 69, 0.1) !important; color: var(--success-color) !important; }
.products-icon { background: rgba(255, 193, 7, 0.1) !important; color: var(--warning-color) !important; }
.customers-icon { background: rgba(220, 53, 69, 0.1) !important; color: var(--danger-color) !important; }

/* ========== FORMULÁRIOS PADRONIZADOS ========== */

.form-control, .form-select {
    height: 42px !important;
    padding: 10px 14px !important;
    font-size: 14px !important;
    border: 1px solid var(--border-color) !important;
    border-radius: var(--border-radius) !important;
    background: var(--white-color) !important;
    transition: var(--transition) !important;
    color: var(--dark-color) !important;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 3px rgba(25, 135, 84, 0.1) !important;
    outline: none !important;
}

.form-label {
    font-size: 14px !important;
    font-weight: 500 !important;
    color: var(--dark-color) !important;
    margin-bottom: 8px !important;
    display: block !important;
}

textarea.form-control {
    height: auto !important;
    min-height: 100px !important;
    resize: vertical !important;
    line-height: 1.5 !important;
}

.form-group {
    margin-bottom: 20px !important;
}

/* Input Groups */
.input-group .form-control {
    border-radius: 0 var(--border-radius) var(--border-radius) 0 !important;
}

.input-group-text {
    background: var(--light-color) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: var(--border-radius) 0 0 var(--border-radius) !important;
    color: var(--secondary-color) !important;
}

/* ========== TABELAS PADRONIZADAS ========== */

.table {
    font-size: 14px !important;
    width: 100% !important;
    margin-bottom: 0 !important;
    border-collapse: separate !important;
    border-spacing: 0 !important;
    background: var(--white-color) !important;
}

.table th {
    height: 45px !important;
    padding: 12px 10px !important;
    font-weight: 600 !important;
    background: var(--light-color) !important;
    border-bottom: 2px solid var(--border-color) !important;
    border-top: none !important;
    color: var(--dark-color) !important;
    white-space: nowrap !important;
    text-overflow: ellipsis !important;
    overflow: hidden !important;
    font-size: 13px !important;
    vertical-align: middle !important;
}

.table td {
    height: 50px !important;
    padding: 12px 10px !important;
    vertical-align: middle !important;
    border-bottom: 1px solid var(--border-color) !important;
    border-top: none !important;
    word-wrap: break-word !important;
    font-size: 13px !important;
    color: var(--dark-color) !important;
}

.table tbody tr:hover {
    background-color: rgba(25, 135, 84, 0.05) !important;
}

.table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: rgba(0, 0, 0, 0.02) !important;
}

.table-striped > tbody > tr:nth-of-type(odd):hover > td {
    background-color: rgba(25, 135, 84, 0.05) !important;
}

/* Tabela Responsiva */
.table-responsive {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
    border-radius: var(--border-radius) !important;
    box-shadow: var(--shadow-sm) !important;
    border: 1px solid var(--border-color) !important;
}

.table-responsive::-webkit-scrollbar {
    height: 8px !important;
}

.table-responsive::-webkit-scrollbar-track {
    background: var(--light-color) !important;
    border-radius: 4px !important;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: var(--secondary-color) !important;
    border-radius: 4px !important;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: var(--dark-color) !important;
}

/* ========== BADGES PADRONIZADOS ========== */

.badge {
    height: 28px !important;
    min-width: 70px !important;
    padding: 6px 12px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    border-radius: var(--border-radius) !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.badge-success {
    background-color: var(--success-color) !important;
    color: white !important;
}

.badge-danger {
    background-color: var(--danger-color) !important;
    color: white !important;
}

.badge-warning {
    background-color: var(--warning-color) !important;
    color: var(--dark-color) !important;
}

.badge-info {
    background-color: var(--info-color) !important;
    color: white !important;
}

.badge-secondary {
    background-color: var(--secondary-color) !important;
    color: white !important;
}

.badge-primary {
    background-color: var(--primary-color) !important;
    color: white !important;
}

/* Status Badges */
.status-badge {
    padding: 6px 12px !important;
    border-radius: 20px !important;
    font-size: 11px !important;
    font-weight: 500 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.status-pending {
    background-color: #fff3cd !important;
    color: #856404 !important;
    border: 1px solid #ffeaa7 !important;
}

.status-completed {
    background-color: #d1ecf1 !important;
    color: #0c5460 !important;
    border: 1px solid #bee5eb !important;
}

.status-cancelled {
    background-color: #f8d7da !important;
    color: #721c24 !important;
    border: 1px solid #f5c6cb !important;
}

.status-paid {
    background-color: #d4edda !important;
    color: #155724 !important;
    border: 1px solid #c3e6cb !important;
}

/* ========== SIDEBAR E NAVEGAÇÃO ========== */

.wrapper {
    display: flex !important;
    min-height: 100vh !important;
}

#sidebar-wrapper {
    min-height: 100vh !important;
    width: 250px !important;
    margin-left: 0 !important;
    transition: margin 0.25s ease-out !important;
    background-color: var(--white-color) !important;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1) !important;
    border-right: 1px solid var(--border-color) !important;
}

#sidebar-wrapper .sidebar-heading {
    padding: 20px 16px !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    border-bottom: 1px solid var(--border-color) !important;
    background: var(--light-color) !important;
    color: var(--dark-color) !important;
    text-align: center !important;
}

#sidebar-wrapper .list-group {
    width: 100% !important;
    border: none !important;
}

#sidebar-wrapper .list-group-item {
    height: 50px !important;
    padding: 12px 20px !important;
    font-size: 14px !important;
    border: none !important;
    border-radius: 0 !important;
    color: var(--dark-color) !important;
    display: flex !important;
    align-items: center !important;
    transition: var(--transition) !important;
    text-decoration: none !important;
    background: transparent !important;
}

#sidebar-wrapper .list-group-item i {
    margin-right: 12px !important;
    width: 20px !important;
    text-align: center !important;
    font-size: 16px !important;
    color: var(--secondary-color) !important;
}

#sidebar-wrapper .list-group-item:hover {
    background-color: rgba(25, 135, 84, 0.1) !important;
    color: var(--primary-color) !important;
    text-decoration: none !important;
}

#sidebar-wrapper .list-group-item:hover i {
    color: var(--primary-color) !important;
}

#sidebar-wrapper .list-group-item.active {
    background-color: var(--primary-color) !important;
    color: white !important;
    border: none !important;
}

#sidebar-wrapper .list-group-item.active i {
    color: white !important;
}

#page-content-wrapper {
    flex: 1 !important;
    min-width: 0 !important;
    padding: 0 !important;
}

/* Navbar */
.navbar {
    background-color: var(--white-color) !important;
    box-shadow: var(--shadow-sm) !important;
    padding: 16px 20px !important;
    border-bottom: 1px solid var(--border-color) !important;
}

.navbar .navbar-toggler {
    padding: 8px 12px !important;
    border-color: var(--primary-color) !important;
    color: var(--primary-color) !important;
    border-radius: var(--border-radius) !important;
}

.navbar .navbar-toggler:focus {
    box-shadow: 0 0 0 3px rgba(25, 135, 84, 0.1) !important;
}

/* ========== MODAIS PADRONIZADOS ========== */

.modal-header {
    height: 70px !important;
    padding: 20px 24px !important;
    border-bottom: 1px solid var(--border-color) !important;
    background: var(--light-color) !important;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0 !important;
}

.modal-title {
    font-size: 18px !important;
    font-weight: 600 !important;
    color: var(--dark-color) !important;
    margin: 0 !important;
}

.modal-body {
    padding: 24px !important;
    background: var(--white-color) !important;
}

.modal-footer {
    height: 80px !important;
    padding: 20px 24px !important;
    border-top: 1px solid var(--border-color) !important;
    background: var(--light-color) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: flex-end !important;
    gap: 12px !important;
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg) !important;
}

.modal-content {
    border: none !important;
    border-radius: var(--border-radius-lg) !important;
    box-shadow: var(--shadow-lg) !important;
}

/* ========== ALERTAS PADRONIZADOS ========== */

.alert {
    padding: 16px 20px !important;
    border-radius: var(--border-radius) !important;
    border: none !important;
    font-size: 14px !important;
    margin-bottom: 20px !important;
    display: flex !important;
    align-items: center !important;
    box-shadow: var(--shadow-sm) !important;
}

.alert i {
    margin-right: 12px !important;
    font-size: 16px !important;
}

.alert-success {
    background-color: #d4edda !important;
    color: #155724 !important;
    border-left: 4px solid var(--success-color) !important;
}

.alert-danger {
    background-color: #f8d7da !important;
    color: #721c24 !important;
    border-left: 4px solid var(--danger-color) !important;
}

.alert-warning {
    background-color: #fff3cd !important;
    color: #856404 !important;
    border-left: 4px solid var(--warning-color) !important;
}

.alert-info {
    background-color: #d1ecf1 !important;
    color: #0c5460 !important;
    border-left: 4px solid var(--info-color) !important;
}

/* ========== PAGINAÇÃO PADRONIZADA ========== */

.pagination {
    margin: 0 !important;
    justify-content: center !important;
}

.pagination .page-link {
    height: 40px !important;
    min-width: 40px !important;
    padding: 8px 12px !important;
    font-size: 14px !important;
    border-radius: var(--border-radius) !important;
    margin: 0 2px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border: 1px solid var(--border-color) !important;
    color: var(--dark-color) !important;
    text-decoration: none !important;
    transition: var(--transition) !important;
}

.pagination .page-link:hover {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
}

/* ========== MODAIS PADRONIZADOS ========== */

.modal-header {
    height: 70px !important;
    padding: 20px 25px !important;
    border-bottom: 1px solid #e2e8f0 !important;
}

.modal-title {
    font-size: 18px !important;
    font-weight: 600 !important;
}

.modal-body {
    padding: 25px !important;
}

.modal-footer {
    height: 80px !important;
    padding: 20px 25px !important;
    border-top: 1px solid #e2e8f0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: flex-end !important;
    gap: 10px !important;
}

/* ========== ALERTAS PADRONIZADOS ========== */

.alert {
    padding: 15px 20px !important;
    border-radius: 8px !important;
    border: none !important;
    font-size: 14px !important;
    margin-bottom: 20px !important;
}

/* ========== PAGINAÇÃO PADRONIZADA ========== */

.pagination .page-link {
    height: 40px !important;
    min-width: 40px !important;
    padding: 8px 12px !important;
    font-size: 14px !important;
    border-radius: 6px !important;
    margin: 0 2px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* ========== OTIMIZAÇÕES ESPECÍFICAS PARA TABELAS ========== */

/* Tabelas de Vendas/Pedidos */
.table-sales th:nth-child(1), .table-sales td:nth-child(1) { width: 8%; text-align: center; }   /* ID */
.table-sales th:nth-child(2), .table-sales td:nth-child(2) { width: 20%; text-align: left; }    /* Cliente */
.table-sales th:nth-child(3), .table-sales td:nth-child(3) { width: 18%; text-align: left; }    /* Produto */
.table-sales th:nth-child(4), .table-sales td:nth-child(4) { width: 12%; text-align: right; }   /* Valor */
.table-sales th:nth-child(5), .table-sales td:nth-child(5) { width: 12%; text-align: center; }  /* Status */
.table-sales th:nth-child(6), .table-sales td:nth-child(6) { width: 15%; text-align: center; }  /* Data */
.table-sales th:nth-child(7), .table-sales td:nth-child(7) { width: 15%; text-align: center; }  /* Ações */

/* Tabelas de Clientes */
.table-customers th:nth-child(1), .table-customers td:nth-child(1) { width: 25%; text-align: left; }    /* Nome */
.table-customers th:nth-child(2), .table-customers td:nth-child(2) { width: 30%; text-align: left; }    /* Email */
.table-customers th:nth-child(3), .table-customers td:nth-child(3) { width: 15%; text-align: center; }  /* Pedidos */
.table-customers th:nth-child(4), .table-customers td:nth-child(4) { width: 15%; text-align: right; }   /* Total */
.table-customers th:nth-child(5), .table-customers td:nth-child(5) { width: 15%; text-align: center; }  /* Ações */

/* Tabelas de Produtos */
.table-products th:nth-child(1), .table-products td:nth-child(1) { width: 8%; text-align: center; }   /* ID */
.table-products th:nth-child(2), .table-products td:nth-child(2) { width: 15%; text-align: center; }  /* Imagem */
.table-products th:nth-child(3), .table-products td:nth-child(3) { width: 35%; text-align: left; }    /* Nome */
.table-products th:nth-child(4), .table-products td:nth-child(4) { width: 15%; text-align: right; }   /* Preço */
.table-products th:nth-child(5), .table-products td:nth-child(5) { width: 12%; text-align: center; }  /* Status */
.table-products th:nth-child(6), .table-products td:nth-child(6) { width: 15%; text-align: center; }  /* Ações */

/* Botões de ação em tabelas */
.table .btn-action {
    height: 32px !important;
    min-width: 32px !important;
    padding: 6px !important;
    font-size: 12px !important;
    margin: 2px !important;
    border-radius: var(--border-radius) !important;
}

.table .btn-action i {
    font-size: 12px !important;
}

/* Badges em tabelas */
.table .badge {
    font-size: 11px !important;
    padding: 4px 8px !important;
    height: 24px !important;
    min-width: 60px !important;
}

/* ========== RESPONSIVIDADE GERAL ========== */

/* Desktop Large */
@media (min-width: 1200px) {
    .container-fluid {
        padding: 24px !important;
    }
}

/* Desktop */
@media (min-width: 992px) {
    #sidebar-wrapper {
        margin-left: 0 !important;
    }

    #page-content-wrapper {
        min-width: 0 !important;
        width: 100% !important;
    }

    #wrapper.toggled #sidebar-wrapper {
        margin-left: -250px !important;
    }
}

/* Tablet */
@media (max-width: 991.98px) {
    #sidebar-wrapper {
        margin-left: -250px !important;
    }

    #wrapper.toggled #sidebar-wrapper {
        margin-left: 0 !important;
    }

    .table th, .table td {
        padding: 8px 6px !important;
        font-size: 12px !important;
    }

    .table th {
        height: 40px !important;
    }

    .table td {
        height: 45px !important;
    }

    .btn {
        height: 36px !important;
        min-width: 90px !important;
        padding: 6px 12px !important;
        font-size: 13px !important;
    }

    .card-header {
        height: 55px !important;
        padding: 12px 16px !important;
    }

    .dashboard-card .card-body {
        height: 100px !important;
        padding: 12px !important;
    }

    .dashboard-card .number {
        font-size: 20px !important;
    }
}

/* Mobile */
@media (max-width: 767.98px) {
    .container-fluid {
        padding: 16px !important;
    }

    .btn {
        height: 34px !important;
        min-width: 80px !important;
        padding: 6px 10px !important;
        font-size: 12px !important;
    }

    .btn-sm {
        height: 28px !important;
        min-width: 60px !important;
        padding: 4px 8px !important;
        font-size: 11px !important;
    }

    .form-control, .form-select {
        height: 38px !important;
        padding: 8px 12px !important;
        font-size: 13px !important;
    }

    .card-header {
        height: 50px !important;
        padding: 10px 12px !important;
        font-size: 14px !important;
    }

    .card-body {
        padding: 16px !important;
    }

    .table th, .table td {
        padding: 6px 4px !important;
        font-size: 11px !important;
    }

    .table th {
        height: 36px !important;
        font-size: 10px !important;
    }

    .table td {
        height: 40px !important;
    }

    .table .btn-action {
        height: 26px !important;
        min-width: 26px !important;
        padding: 4px !important;
        font-size: 10px !important;
    }

    .table .badge {
        font-size: 9px !important;
        padding: 2px 6px !important;
        height: 20px !important;
        min-width: 40px !important;
    }

    .list-group-item {
        height: 45px !important;
        padding: 10px 16px !important;
        font-size: 13px !important;
    }

    .dashboard-card .card-body {
        height: 90px !important;
        padding: 10px !important;
    }

    .dashboard-card .card-icon {
        width: 40px !important;
        height: 40px !important;
        font-size: 16px !important;
    }

    .dashboard-card .number {
        font-size: 18px !important;
    }

    .dashboard-card .label {
        font-size: 12px !important;
    }

    .modal-header {
        height: 60px !important;
        padding: 16px 20px !important;
    }

    .modal-body {
        padding: 20px !important;
    }

    .modal-footer {
        height: 70px !important;
        padding: 16px 20px !important;
    }

    /* Ocultar colunas menos importantes em mobile */
    .table-sales th:nth-child(3), .table-sales td:nth-child(3) { display: none; } /* Produto */
    .table-sales th:nth-child(6), .table-sales td:nth-child(6) { display: none; } /* Data */

    .table-customers th:nth-child(3), .table-customers td:nth-child(3) { display: none; } /* Pedidos */
    .table-customers th:nth-child(4), .table-customers td:nth-child(4) { display: none; } /* Total */

    .table-products th:nth-child(2), .table-products td:nth-child(2) { display: none; } /* Imagem */
    .table-products th:nth-child(4), .table-products td:nth-child(4) { display: none; } /* Preço */
}

/* Mobile Small */
@media (max-width: 575.98px) {
    .container-fluid {
        padding: 12px !important;
    }

    .btn {
        height: 32px !important;
        min-width: 70px !important;
        padding: 4px 8px !important;
        font-size: 11px !important;
    }

    .card-header {
        height: 45px !important;
        padding: 8px 10px !important;
        font-size: 13px !important;
    }

    .table th, .table td {
        padding: 4px 2px !important;
        font-size: 10px !important;
    }

    .table th {
        height: 32px !important;
        font-size: 9px !important;
    }

    .table td {
        height: 36px !important;
    }

    .dashboard-card .card-body {
        height: 80px !important;
        padding: 8px !important;
    }

    .dashboard-card .number {
        font-size: 16px !important;
    }

    .dashboard-card .label {
        font-size: 11px !important;
    }
}

/* ========== UTILITÁRIOS ADICIONAIS ========== */

/* Texto truncado */
.text-truncate-custom {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 150px !important;
}

/* Espaçamentos padronizados */
.mb-2 { margin-bottom: 8px !important; }
.mb-3 { margin-bottom: 16px !important; }
.mb-4 { margin-bottom: 24px !important; }
.mb-5 { margin-bottom: 32px !important; }

.mt-2 { margin-top: 8px !important; }
.mt-3 { margin-top: 16px !important; }
.mt-4 { margin-top: 24px !important; }
.mt-5 { margin-top: 32px !important; }

/* Animações suaves */
.fade-in {
    animation: fadeIn 0.5s ease-in !important;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Melhorias de acessibilidade */
.btn:focus, .form-control:focus, .form-select:focus {
    outline: 2px solid var(--primary-color) !important;
    outline-offset: 2px !important;
}

/* Estados de loading */
.loading {
    opacity: 0.6 !important;
    pointer-events: none !important;
    cursor: wait !important;
}

/* ========== FIM DO CSS PADRONIZADO ========== */
