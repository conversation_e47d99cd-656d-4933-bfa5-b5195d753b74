<?php
require_once 'includes/auth_check.php';
require_once 'database/connection.php';

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();

    // Estatísticas principais
    $stmt = $pdo->query("SELECT COUNT(*) FROM sales");
    $totalSales = $stmt->fetchColumn();

    $stmt = $pdo->query("SELECT COALESCE(SUM(total_amount), 0) FROM sales WHERE payment_status = 'approved'");
    $totalRevenue = $stmt->fetchColumn();

    $stmt = $pdo->query("SELECT COUNT(*) FROM products WHERE status = 'active'");
    $totalProducts = $stmt->fetchColumn();

    $stmt = $pdo->query("SELECT COUNT(*) FROM customers");
    $totalCustomers = $stmt->fetchColumn();

    // Vendas recentes
    $stmt = $pdo->query("
        SELECT s.*, p.name as product_name
        FROM sales s
        LEFT JOIN products p ON s.product_id = p.id
        ORDER BY s.created_at DESC
        LIMIT 5
    ");
    $recentSales = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    $error = 'Erro ao carregar dados do dashboard: ' . $e->getMessage();
    $totalSales = $totalRevenue = $totalProducts = $totalCustomers = 0;
    $recentSales = [];
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/standardized.css">
</head>
<body>
    <div class="d-flex" id="wrapper">
        <?php include 'includes/sidebar.php'; ?>

        <div id="page-content-wrapper">
            <?php include 'includes/navbar.php'; ?>

            <div class="container-fluid px-4">
                <div class="row">
                    <div class="col-lg-12">
                        <h1 class="mt-4 mb-4">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            Dashboard
                        </h1>

                        <?php if (isset($error)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                        <?php endif; ?>

                        <!-- Cards de Estatísticas -->
                        <div class="row mb-4">
                            <div class="col-lg-3 col-md-6 mb-4">
                                <div class="card dashboard-card">
                                    <div class="card-body">
                                        <div class="card-icon sales-icon">
                                            <i class="fas fa-chart-line"></i>
                                        </div>
                                        <div class="card-content">
                                            <h3><?php echo number_format($totalSales); ?></h3>
                                            <p>Total de Vendas</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-md-6 mb-4">
                                <div class="card dashboard-card">
                                    <div class="card-body">
                                        <div class="card-icon revenue-icon">
                                            <i class="fas fa-coins"></i>
                                        </div>
                                        <div class="card-content">
                                            <h3>R$ <?php echo number_format($totalRevenue, 2, ',', '.'); ?></h3>
                                            <p>Receita Total</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-md-6 mb-4">
                                <div class="card dashboard-card">
                                    <div class="card-body">
                                        <div class="card-icon products-icon">
                                            <i class="fas fa-cubes"></i>
                                        </div>
                                        <div class="card-content">
                                            <h3><?php echo number_format($totalProducts); ?></h3>
                                            <p>Produtos Ativos</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-md-6 mb-4">
                                <div class="card dashboard-card">
                                    <div class="card-body">
                                        <div class="card-icon customers-icon">
                                            <i class="fas fa-user-friends"></i>
                                        </div>
                                        <div class="card-content">
                                            <h3><?php echo number_format($totalCustomers); ?></h3>
                                            <p>Total de Clientes</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Seção Principal -->
                        <div class="row mb-4">
                            <!-- Vendas dos Últimos 7 Dias -->
                            <div class="col-lg-8">
                                <div class="card dashboard-card">
                                    <div class="card-body">
                                        <h5 class="card-title mb-3">
                                            <i class="fas fa-chart-area me-2"></i>
                                            Vendas dos Últimos 7 Dias
                                        </h5>
                                        <div class="text-center text-muted py-5">
                                            <i class="fas fa-chart-line fa-3x mb-3"></i>
                                            <p>Gráfico de vendas será exibido aqui</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Status das Vendas -->
                            <div class="col-lg-4">
                                <div class="card dashboard-card">
                                    <div class="card-body">
                                        <h5 class="card-title mb-3">
                                            <i class="fas fa-chart-pie me-2"></i>
                                            Status das Vendas
                                        </h5>
                                        <div class="text-center text-muted py-4">
                                            <i class="fas fa-chart-pie fa-2x mb-3"></i>
                                            <p>Gráfico de status será exibido aqui</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Seção Inferior -->
                        <div class="row mb-4">
                            <!-- Produtos Mais Vendidos -->
                            <div class="col-lg-6">
                                <div class="card dashboard-card">
                                    <div class="card-body">
                                        <h5 class="card-title mb-3">
                                            <i class="fas fa-trophy me-2"></i>
                                            Produtos Mais Vendidos
                                        </h5>
                                        <div class="table-responsive">
                                            <table class="table table-dashboard">
                                                <thead>
                                                    <tr>
                                                        <th>Produto</th>
                                                        <th>Vendas</th>
                                                        <th>Receita</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td colspan="3" class="text-center text-muted py-3">
                                                            <i class="fas fa-inbox me-2"></i>
                                                            Nenhum produto vendido ainda
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Vendas Recentes -->
                            <div class="col-lg-6">
                                <div class="card dashboard-card">
                                    <div class="card-body">
                                        <h5 class="card-title mb-3">
                                            <i class="fas fa-clock me-2"></i>
                                            Vendas Recentes
                                        </h5>
                                        <div class="table-responsive">
                                            <table class="table table-dashboard">
                                                <thead>
                                                    <tr>
                                                        <th>ID</th>
                                                        <th>Cliente</th>
                                                        <th>Valor</th>
                                                        <th>Status</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php if (empty($recentSales)): ?>
                                                    <tr>
                                                        <td colspan="4" class="text-center text-muted py-3">
                                                            <i class="fas fa-inbox me-2"></i>
                                                            Nenhuma venda encontrada
                                                        </td>
                                                    </tr>
                                                    <?php else: ?>
                                                    <?php foreach ($recentSales as $sale): ?>
                                                    <tr>
                                                        <td>#<?php echo str_pad($sale['id'], 5, '0', STR_PAD_LEFT); ?></td>
                                                        <td><?php echo htmlspecialchars($sale['customer_name']); ?></td>
                                                        <td>R$ <?php echo number_format($sale['total_amount'], 2, ',', '.'); ?></td>
                                                        <td>
                                                            <span class="status-badge status-<?php echo $sale['payment_status']; ?>">
                                                                <?php
                                                                $status_labels = [
                                                                    'pending' => 'Pendente',
                                                                    'approved' => 'Aprovado',
                                                                    'paid' => 'Pago',
                                                                    'completed' => 'Concluído',
                                                                    'cancelled' => 'Cancelado',
                                                                    'refunded' => 'Reembolsado'
                                                                ];
                                                                echo $status_labels[$sale['payment_status']] ?? $sale['payment_status'];
                                                                ?>
                                                            </span>
                                                        </td>
                                                    </tr>
                                                    <?php endforeach; ?>
                                                    <?php endif; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="text-center mt-3">
                                            <a href="sales.php" class="btn btn-success btn-sm">
                                                <i class="fas fa-eye me-1"></i>
                                                Ver Todas as Vendas
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Ações Rápidas -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card dashboard-card">
                                    <div class="card-body">
                                        <h5 class="card-title mb-3">
                                            <i class="fas fa-bolt me-2"></i>
                                            Ações Rápidas
                                        </h5>
                                        <div class="row">
                                            <div class="col-md-2 mb-2">
                                                <a href="add_product.php" class="btn btn-outline-primary w-100 btn-sm">
                                                    <i class="fas fa-plus me-1"></i>
                                                    Adicionar Produto
                                                </a>
                                            </div>
                                            <div class="col-md-2 mb-2">
                                                <a href="sales.php" class="btn btn-outline-success w-100 btn-sm">
                                                    <i class="fas fa-shopping-cart me-1"></i>
                                                    Ver Vendas
                                                </a>
                                            </div>
                                            <div class="col-md-2 mb-2">
                                                <a href="customers.php" class="btn btn-outline-info w-100 btn-sm">
                                                    <i class="fas fa-users me-1"></i>
                                                    Ver Clientes
                                                </a>
                                            </div>
                                            <div class="col-md-2 mb-2">
                                                <a href="reports.php" class="btn btn-outline-warning w-100 btn-sm">
                                                    <i class="fas fa-chart-bar me-1"></i>
                                                    Relatórios
                                                </a>
                                            </div>
                                            <div class="col-md-2 mb-2">
                                                <a href="settings.php" class="btn btn-outline-secondary w-100 btn-sm">
                                                    <i class="fas fa-cog me-1"></i>
                                                    Configurações
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/scripts.js"></script>
</body>
</html>


