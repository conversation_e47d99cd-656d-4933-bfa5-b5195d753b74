<?php
require_once 'includes/header.php';

// Buscar estatísticas
$db = Database::getInstance();
$pdo = $db->getConnection();

// Total de vendas
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count, SUM(CASE WHEN payment_status = 'paid' THEN amount ELSE 0 END) as total FROM orders");
    $sales = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $sales = ['count' => 0, 'total' => 0];
}

// Total de produtos
$stmt = $pdo->query("SELECT COUNT(*) as count FROM products");
$products = $stmt->fetch(PDO::FETCH_ASSOC);

// Total de clientes
$stmt = $pdo->query("SELECT COUNT(DISTINCT customer_email) as count FROM orders");
$customers = $stmt->fetch(PDO::FETCH_ASSOC);

// Vendas recentes
$stmt = $pdo->query("
    SELECT o.*, p.name as product_name
    FROM orders o
    LEFT JOIN products p ON o.product_id = p.id
    ORDER BY o.created_at DESC
    LIMIT 12
");
$recentSales = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Produtos mais vendidos
$stmt = $pdo->query("
    SELECT p.*, COUNT(o.id) as sales_count 
    FROM products p 
    LEFT JOIN orders o ON p.id = o.product_id 
    GROUP BY p.id 
    ORDER BY sales_count DESC 
    LIMIT 5
");
$topProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Função para obter estatísticas do chat
function getChatStats($pdo) {
    $stats = [];
    
    // Total de usuários
    $query = "SELECT COUNT(*) as total FROM chat_users WHERE is_admin = 0";
    try {
        $stmt = $pdo->query($query);
        $stats['total_users'] = $stmt->fetchColumn();
    } catch (Exception $e) {
        $stats['total_users'] = 0;
    }
    
    // Usuários ativos (últimas 24h)
    $query = "SELECT COUNT(*) as active FROM chat_users WHERE is_admin = 0 AND last_activity >= DATE_SUB(NOW(), INTERVAL 24 HOUR)";
    try {
        $stmt = $pdo->query($query);
        $stats['active_users'] = $stmt->fetchColumn();
    } catch (Exception $e) {
        $stats['active_users'] = 0;
    }
    
    // Total de mensagens
    $query = "SELECT COUNT(*) as total FROM chat_messages";
    try {
        $stmt = $pdo->query($query);
        $stats['total_messages'] = $stmt->fetchColumn();
    } catch (Exception $e) {
        $stats['total_messages'] = 0;
    }
    
    // Conversas ativas
    $query = "SELECT COUNT(DISTINCT user_id) as active FROM chat_messages WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
    try {
        $stmt = $pdo->query($query);
        $stats['active_conversations'] = $stmt->fetchColumn();
    } catch (Exception $e) {
        $stats['active_conversations'] = 0;
    }
    
    return $stats;
}

// Função para obter logs de usuários
function getUserLogs($pdo) {
    $query = "
        SELECT 
            cu.name,
            cu.email,
            cu.whatsapp,
            cu.last_activity,
            COUNT(cm.id) as message_count
        FROM chat_users cu
        LEFT JOIN chat_messages cm ON cu.id = cm.user_id
        WHERE cu.is_admin = 0
        GROUP BY cu.id
        ORDER BY cu.last_activity DESC
        LIMIT 10
    ";
    
    try {
        $stmt = $pdo->query($query);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        return [];
    }
}

function formatDateTime($dateStr) {
    return date('d/m/Y H:i', strtotime($dateStr));
}

$stats = getChatStats($pdo);
$userLogs = getUserLogs($pdo);
?>

<!-- Dashboard Content -->
<div class="container-fluid p-4">
    <!-- Page Title -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="fs-2 m-0">Dashboard</h2>
        <div class="text-muted">
            <i class="fas fa-clock me-1"></i>
            <?php echo date('d/m/Y H:i'); ?>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="dashboard-stats-grid">
        <!-- Vendas Card -->
        <div class="dashboard-card">
            <div class="card-body">
                <div class="card-icon sales-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="card-content">
                    <h3><?php echo number_format($sales['count']); ?></h3>
                    <p>Total de Vendas</p>
                </div>
            </div>
        </div>

        <!-- Receita Card -->
        <div class="dashboard-card">
            <div class="card-body">
                <div class="card-icon revenue-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="card-content">
                    <h3>R$ <?php echo number_format($sales['total'], 2, ',', '.'); ?></h3>
                    <p>Receita Total</p>
                </div>
            </div>
        </div>

        <!-- Produtos Card -->
        <div class="dashboard-card">
            <div class="card-body">
                <div class="card-icon products-icon">
                    <i class="fas fa-box"></i>
                </div>
                <div class="card-content">
                    <h3><?php echo number_format($products['count']); ?></h3>
                    <p>Total de Produtos</p>
                </div>
            </div>
        </div>

        <!-- Clientes Card -->
        <div class="dashboard-card">
            <div class="card-body">
                <div class="card-icon customers-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="card-content">
                    <h3><?php echo number_format($customers['count']); ?></h3>
                    <p>Total de Clientes</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Chat Stats Cards -->
    <div class="dashboard-stats-grid">
        <!-- Total Usuários Chat -->
        <div class="dashboard-card">
            <div class="card-body">
                <div class="card-icon" style="background-color: rgba(76, 175, 80, 0.1); color: #4CAF50;">
                    <i class="fas fa-users"></i>
                </div>
                <div class="card-content">
                    <h3><?php echo number_format($stats['total_users']); ?></h3>
                    <p>Usuários do Chat</p>
                </div>
            </div>
        </div>

        <!-- Usuários Ativos -->
        <div class="dashboard-card">
            <div class="card-body">
                <div class="card-icon" style="background-color: rgba(3, 169, 244, 0.1); color: #03A9F4;">
                    <i class="fas fa-user-clock"></i>
                </div>
                <div class="card-content">
                    <h3><?php echo number_format($stats['active_users']); ?></h3>
                    <p>Ativos (24h)</p>
                </div>
            </div>
        </div>

        <!-- Total Mensagens -->
        <div class="dashboard-card">
            <div class="card-body">
                <div class="card-icon" style="background-color: rgba(255, 152, 0, 0.1); color: #FF9800;">
                    <i class="fas fa-comments"></i>
                </div>
                <div class="card-content">
                    <h3><?php echo number_format($stats['total_messages']); ?></h3>
                    <p>Total Mensagens</p>
                </div>
            </div>
        </div>

        <!-- Conversas Ativas -->
        <div class="dashboard-card">
            <div class="card-body">
                <div class="card-icon" style="background-color: rgba(33, 150, 243, 0.1); color: #2196F3;">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="card-content">
                    <h3><?php echo number_format($stats['active_conversations']); ?></h3>
                    <p>Conversas (7 dias)</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tables Row -->
    <div class="row">
        <!-- Recent Sales -->
        <div class="col-lg-8 mb-4">
            <div class="dashboard-table-card">
                <div class="card-header">
                    <i class="fas fa-shopping-cart me-2"></i>
                    Vendas Recentes
                </div>
                <div class="card-body">
                    <div class="table-responsive-dashboard">
                        <table class="table table-dashboard table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Cliente</th>
                                    <th>Produto</th>
                                    <th>Valor</th>
                                    <th>Status</th>
                                    <th>Método</th>
                                    <th>Data</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($recentSales)): ?>
                                    <tr>
                                        <td colspan="8" class="text-center text-muted py-4">
                                            <i class="fas fa-inbox fa-2x mb-2 d-block"></i>
                                            Nenhuma venda encontrada
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($recentSales as $sale): ?>
                                        <tr>
                                            <td class="text-center">
                                                <strong>#<?php echo $sale['id']; ?></strong>
                                            </td>
                                            <td>
                                                <div class="fw-semibold"><?php echo htmlspecialchars(substr($sale['customer_name'] ?? 'Cliente', 0, 15)); ?></div>
                                                <small class="text-muted"><?php echo htmlspecialchars(substr($sale['customer_email'], 0, 20)); ?></small>
                                            </td>
                                            <td>
                                                <div class="fw-medium text-truncate" title="<?php echo htmlspecialchars($sale['product_name'] ?? $sale['product_id']); ?>">
                                                    <?php echo htmlspecialchars(substr($sale['product_name'] ?? $sale['product_id'], 0, 18)); ?>
                                                </div>
                                                <small class="text-muted">ID: <?php echo $sale['product_id']; ?></small>
                                            </td>
                                            <td class="text-end">
                                                <strong class="text-success">R$ <?php echo number_format($sale['amount'] ?? 0, 2, ',', '.'); ?></strong>
                                            </td>
                                            <td class="text-center">
                                                <?php
                                                $statusClass = $sale['payment_status'] == 'paid' ? 'success' : 'warning';
                                                $statusText = $sale['payment_status'] == 'paid' ? 'Pago' : 'Pendente';
                                                ?>
                                                <span class="badge badge-<?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                            </td>
                                            <td class="text-center">
                                                <small class="text-muted"><?php echo ucfirst($sale['payment_method'] ?? 'N/A'); ?></small>
                                            </td>
                                            <td class="text-center">
                                                <div class="text-muted">
                                                    <div><?php echo date('d/m/Y', strtotime($sale['created_at'])); ?></div>
                                                    <small><?php echo date('H:i', strtotime($sale['created_at'])); ?></small>
                                                </div>
                                            </td>
                                            <td class="text-center">
                                                <a href="view_order.php?id=<?php echo $sale['id']; ?>" class="btn btn-sm btn-outline-primary btn-action" title="Ver detalhes">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Activity Logs -->
        <div class="col-lg-4 mb-4">
            <div class="dashboard-table-card">
                <div class="card-header">
                    <i class="fas fa-user-clock me-2"></i>
                    Atividade de Usuários
                </div>
                <div class="card-body">
                    <div class="table-responsive-dashboard">
                        <table class="table table-dashboard table-hover">
                            <thead>
                                <tr>
                                    <th>Usuário</th>
                                    <th>Contato</th>
                                    <th>Msgs</th>
                                    <th>Última Atividade</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($userLogs)): ?>
                                    <tr>
                                        <td colspan="4" class="text-center text-muted py-4">
                                            <i class="fas fa-user-slash fa-2x mb-2 d-block"></i>
                                            Nenhuma atividade encontrada
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($userLogs as $log): ?>
                                        <tr>
                                            <td>
                                                <div class="fw-semibold"><?php echo htmlspecialchars($log['name']); ?></div>
                                                <small class="text-muted text-truncate d-block"><?php echo htmlspecialchars(substr($log['email'], 0, 25)); ?></small>
                                            </td>
                                            <td>
                                                <?php if (!empty($log['whatsapp'])): ?>
                                                <div class="fw-medium">
                                                    <i class="fab fa-whatsapp me-1 text-success"></i>
                                                    <?php echo htmlspecialchars($log['whatsapp']); ?>
                                                </div>
                                                <?php else: ?>
                                                <small class="text-muted">Não informado</small>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-center">
                                                <strong class="text-primary"><?php echo $log['message_count']; ?></strong>
                                                <small class="text-muted d-block">mensagens</small>
                                            </td>
                                            <td class="text-center">
                                                <div class="text-muted">
                                                    <?php
                                                    if (!empty($log['last_activity'])) {
                                                        $interval = date_diff(new DateTime($log['last_activity']), new DateTime());
                                                        if ($interval->days > 0) {
                                                            echo '<div class="fw-medium">' . $interval->days . ' dia(s)</div>';
                                                            echo '<small>atrás</small>';
                                                        } elseif ($interval->h > 0) {
                                                            echo '<div class="fw-medium">' . $interval->h . ' hora(s)</div>';
                                                            echo '<small>atrás</small>';
                                                        } elseif ($interval->i > 0) {
                                                            echo '<div class="fw-medium">' . $interval->i . ' min</div>';
                                                            echo '<small>atrás</small>';
                                                        } else {
                                                            echo '<div class="fw-medium text-success">Agora</div>';
                                                        }
                                                    } else {
                                                        echo '<div class="fw-medium">Nunca</div>';
                                                    }
                                                    ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
