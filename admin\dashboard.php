<?php
require_once 'session.php';
require_once 'database/connection.php';

// Headers para evitar cache
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();

    // Estatísticas principais
    $stmt = $pdo->query("SELECT COUNT(*) FROM sales");
    $totalSales = $stmt->fetchColumn();

    $stmt = $pdo->query("SELECT COALESCE(SUM(total_amount), 0) FROM sales WHERE payment_status = 'approved'");
    $totalRevenue = $stmt->fetchColumn();

    $stmt = $pdo->query("SELECT COUNT(*) FROM products WHERE status = 'active'");
    $totalProducts = $stmt->fetchColumn();

    $stmt = $pdo->query("SELECT COUNT(*) FROM customers");
    $totalCustomers = $stmt->fetchColumn();

    // Vendas dos últimos 7 dias
    $stmt = $pdo->query("
        SELECT DATE(created_at) as date, COUNT(*) as count, COALESCE(SUM(total_amount), 0) as revenue
        FROM sales 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY DATE(created_at)
        ORDER BY date ASC
    ");
    $salesData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Status das vendas
    $stmt = $pdo->query("
        SELECT payment_status, COUNT(*) as count 
        FROM sales 
        GROUP BY payment_status
    ");
    $statusData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Produtos mais vendidos
    $stmt = $pdo->query("
        SELECT p.name, COUNT(s.id) as sales_count, COALESCE(SUM(s.total_amount), 0) as revenue
        FROM sales s
        JOIN products p ON s.product_id = p.id
        WHERE s.payment_status = 'approved'
        GROUP BY s.product_id, p.name
        ORDER BY sales_count DESC
        LIMIT 5
    ");
    $topProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Vendas recentes
    $stmt = $pdo->query("
        SELECT s.*, p.name as product_name
        FROM sales s
        LEFT JOIN products p ON s.product_id = p.id
        ORDER BY s.created_at DESC
        LIMIT 10
    ");
    $recentSales = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    $error = 'Erro ao carregar dados do dashboard: ' . $e->getMessage();
    $totalSales = $totalRevenue = $totalProducts = $totalCustomers = 0;
    $salesData = $statusData = $topProducts = $recentSales = [];
}

// Preparar dados para gráficos
$salesDates = [];
$salesCounts = [];
$salesRevenue = [];

for ($i = 6; $i >= 0; $i--) {
    $date = date('Y-m-d', strtotime("-$i days"));
    $salesDates[] = date('d/m', strtotime($date));
    
    $found = false;
    foreach ($salesData as $data) {
        if ($data['date'] === $date) {
            $salesCounts[] = (int)$data['count'];
            $salesRevenue[] = (float)$data['revenue'];
            $found = true;
            break;
        }
    }
    
    if (!$found) {
        $salesCounts[] = 0;
        $salesRevenue[] = 0;
    }
}

$statusColors = [
    'pending' => '#ffc107',
    'approved' => '#28a745',
    'paid' => '#28a745',
    'completed' => '#17a2b8',
    'cancelled' => '#dc3545',
    'refunded' => '#6c757d'
];
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background-color: #f8f9fa;
        }
        .dashboard-card {
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            margin-bottom: 25px;
            border: 1px solid rgba(0,0,0,0.05);
            overflow: hidden;
        }
        .dashboard-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        .dashboard-card .card-body {
            padding: 24px;
            display: flex;
            align-items: center;
            text-align: center;
            position: relative;
        }
        .card-icon {
            width: 64px;
            height: 64px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            position: relative;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .card-icon i {
            font-size: 28px;
            color: #fff;
            font-weight: 900;
        }
        .sales-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        .revenue-icon {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            box-shadow: 0 4px 15px rgba(17, 153, 142, 0.4);
        }
        .products-icon {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            box-shadow: 0 4px 15px rgba(252, 182, 159, 0.4);
        }
        .products-icon i {
            color: #e67e22 !important;
        }
        .customers-icon {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            box-shadow: 0 4px 15px rgba(255, 154, 158, 0.4);
        }
        .customers-icon i {
            color: #e74c3c !important;
        }
        .card-content h3 {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 8px;
            line-height: 1.2;
        }
        .card-content p {
            color: #7f8c8d;
            font-size: 0.9rem;
            margin-bottom: 0;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        .table-dashboard {
            font-size: 0.9rem;
        }
        .table-dashboard th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
            color: #495057;
            padding: 12px 8px;
        }
        .table-dashboard td {
            padding: 12px 8px;
            vertical-align: middle;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        .status-pending { background-color: #fff3cd; color: #856404; }
        .status-approved { background-color: #d4edda; color: #155724; }
        .status-paid { background-color: #d4edda; color: #155724; }
        .status-completed { background-color: #d1ecf1; color: #0c5460; }
        .status-cancelled { background-color: #f8d7da; color: #721c24; }
        .status-refunded { background-color: #e2e3e5; color: #383d41; }
    </style>
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <div class="col-md-9 col-lg-10 main-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="mb-0">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        Dashboard
                    </h2>
                    <div class="text-muted">
                        <i class="fas fa-calendar me-1"></i>
                        <?php echo date('d/m/Y H:i'); ?>
                    </div>
                </div>

                <?php if (isset($error)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
                <?php endif; ?>

                <!-- Cards de Estatísticas -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card dashboard-card h-100">
                            <div class="card-body">
                                <div class="card-icon sales-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="card-content">
                                    <h3><?php echo number_format($totalSales); ?></h3>
                                    <p>Total de Vendas</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card dashboard-card h-100">
                            <div class="card-body">
                                <div class="card-icon revenue-icon">
                                    <i class="fas fa-coins"></i>
                                </div>
                                <div class="card-content">
                                    <h3>R$ <?php echo number_format($totalRevenue, 2, ',', '.'); ?></h3>
                                    <p>Receita Total</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card dashboard-card h-100">
                            <div class="card-body">
                                <div class="card-icon products-icon">
                                    <i class="fas fa-cubes"></i>
                                </div>
                                <div class="card-content">
                                    <h3><?php echo number_format($totalProducts); ?></h3>
                                    <p>Produtos Ativos</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card dashboard-card h-100">
                            <div class="card-body">
                                <div class="card-icon customers-icon">
                                    <i class="fas fa-user-friends"></i>
                                </div>
                                <div class="card-content">
                                    <h3><?php echo number_format($totalCustomers); ?></h3>
                                    <p>Total de Clientes</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Gráficos -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="card dashboard-card">
                            <div class="card-body">
                                <h5 class="card-title mb-3">
                                    <i class="fas fa-chart-area me-2"></i>
                                    Vendas dos Últimos 7 Dias
                                </h5>
                                <div class="chart-container">
                                    <canvas id="salesChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card dashboard-card">
                            <div class="card-body">
                                <h5 class="card-title mb-3">
                                    <i class="fas fa-chart-pie me-2"></i>
                                    Status das Vendas
                                </h5>
                                <div class="chart-container">
                                    <canvas id="statusChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tabelas -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card dashboard-card">
                            <div class="card-body">
                                <h5 class="card-title mb-3">
                                    <i class="fas fa-trophy me-2"></i>
                                    Produtos Mais Vendidos
                                </h5>
                                <div class="table-responsive">
                                    <table class="table table-dashboard">
                                        <thead>
                                            <tr>
                                                <th>Produto</th>
                                                <th>Vendas</th>
                                                <th>Receita</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (empty($topProducts)): ?>
                                            <tr>
                                                <td colspan="3" class="text-center text-muted">
                                                    <i class="fas fa-inbox me-2"></i>
                                                    Nenhum produto vendido ainda
                                                </td>
                                            </tr>
                                            <?php else: ?>
                                            <?php foreach ($topProducts as $product): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($product['name']); ?></td>
                                                <td><?php echo number_format($product['sales_count']); ?></td>
                                                <td>R$ <?php echo number_format($product['revenue'], 2, ',', '.'); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card dashboard-card">
                            <div class="card-body">
                                <h5 class="card-title mb-3">
                                    <i class="fas fa-clock me-2"></i>
                                    Vendas Recentes
                                </h5>
                                <div class="table-responsive">
                                    <table class="table table-dashboard">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Cliente</th>
                                                <th>Valor</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (empty($recentSales)): ?>
                                            <tr>
                                                <td colspan="4" class="text-center text-muted">
                                                    <i class="fas fa-inbox me-2"></i>
                                                    Nenhuma venda encontrada
                                                </td>
                                            </tr>
                                            <?php else: ?>
                                            <?php foreach ($recentSales as $sale): ?>
                                            <tr>
                                                <td>#<?php echo str_pad($sale['id'], 5, '0', STR_PAD_LEFT); ?></td>
                                                <td><?php echo htmlspecialchars($sale['customer_name']); ?></td>
                                                <td>R$ <?php echo number_format($sale['total_amount'], 2, ',', '.'); ?></td>
                                                <td>
                                                    <span class="status-badge status-<?php echo $sale['payment_status']; ?>">
                                                        <?php
                                                        $status_labels = [
                                                            'pending' => 'Pendente',
                                                            'approved' => 'Aprovado',
                                                            'paid' => 'Pago',
                                                            'completed' => 'Concluído',
                                                            'cancelled' => 'Cancelado',
                                                            'refunded' => 'Reembolsado'
                                                        ];
                                                        echo $status_labels[$sale['payment_status']] ?? $sale['payment_status'];
                                                        ?>
                                                    </span>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Links Rápidos -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card dashboard-card">
                            <div class="card-body">
                                <h5 class="card-title mb-3">
                                    <i class="fas fa-bolt me-2"></i>
                                    Ações Rápidas
                                </h5>
                                <div class="row">
                                    <div class="col-md-3 mb-2">
                                        <a href="add_product.php" class="btn btn-outline-primary w-100">
                                            <i class="fas fa-plus me-2"></i>
                                            Adicionar Produto
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="sales.php" class="btn btn-outline-success w-100">
                                            <i class="fas fa-shopping-cart me-2"></i>
                                            Ver Vendas
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="customers.php" class="btn btn-outline-info w-100">
                                            <i class="fas fa-users me-2"></i>
                                            Gerenciar Clientes
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="reports.php" class="btn btn-outline-warning w-100">
                                            <i class="fas fa-chart-bar me-2"></i>
                                            Relatórios
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // Gráfico de Vendas dos Últimos 7 Dias
    const salesCtx = document.getElementById('salesChart').getContext('2d');
    const salesGradient = salesCtx.createLinearGradient(0, 0, 0, 400);
    salesGradient.addColorStop(0, 'rgba(102, 126, 234, 0.3)');
    salesGradient.addColorStop(1, 'rgba(102, 126, 234, 0)');

    new Chart(salesCtx, {
        type: 'line',
        data: {
            labels: <?php echo json_encode($salesDates); ?>,
            datasets: [{
                label: 'Vendas',
                data: <?php echo json_encode($salesCounts); ?>,
                borderColor: '#667eea',
                backgroundColor: salesGradient,
                tension: 0.4,
                fill: true,
                pointBackgroundColor: '#667eea',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 4,
                pointHoverRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });

    // Gráfico de Status das Vendas
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: <?php echo json_encode(array_column($statusData, 'payment_status')); ?>,
            datasets: [{
                data: <?php echo json_encode(array_column($statusData, 'count')); ?>,
                backgroundColor: [
                    '#ffc107',
                    '#28a745',
                    '#17a2b8',
                    '#dc3545',
                    '#6c757d'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });
    </script>
</body>
</html>
