<?php
session_start();

// Headers para evitar cache
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Se já estiver logado, redireciona para o painel de vendas
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    header('Location: sales.php');
    exit;
}

require_once 'database/connection.php';

$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email'] ?? '');
    $password = trim($_POST['password'] ?? '');

    // Debug: Log da tentativa de login
    error_log("Tentativa de login: Email = $email, Senha = " . (empty($password) ? 'vazia' : 'preenchida'));

    if (empty($email) || empty($password)) {
        $error = 'Por favor, preencha todos os campos';
        error_log("Login falhou: campos vazios");
    } else {
        try {
            $db = Database::getInstance();
            $pdo = $db->getConnection();

            // Debug: Verificar se conseguiu conectar
            error_log("Conexão com banco estabelecida");

            $stmt = $pdo->prepare("SELECT * FROM admins WHERE email = ? LIMIT 1");
            $stmt->execute([$email]);
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);

            // Debug: Log do resultado da busca
            if ($admin) {
                error_log("Admin encontrado: ID = {$admin['id']}, Nome = {$admin['name']}");
            } else {
                error_log("Admin não encontrado para email: $email");
            }

            if ($admin && password_verify($password, $admin['password'])) {
                error_log("Senha verificada com sucesso para admin ID: {$admin['id']}");

                $_SESSION['admin_logged_in'] = true;
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['admin_name'] = $admin['name'];
                $_SESSION['admin_email'] = $admin['email'];

                // Atualiza último acesso
                try {
                    $stmt = $pdo->prepare("UPDATE admins SET updated_at = NOW() WHERE id = ?");
                    $stmt->execute([$admin['id']]);
                    error_log("Último acesso atualizado para admin ID: {$admin['id']}");
                } catch (Exception $e) {
                    error_log("Erro ao atualizar último acesso: " . $e->getMessage());
                }

                error_log("Redirecionando para sales.php");
                header('Location: sales.php');
                exit;
            } else {
                if (!$admin) {
                    $error = 'Email não encontrado';
                    error_log("Login falhou: email não encontrado - $email");
                } else {
                    $error = 'Senha incorreta';
                    error_log("Login falhou: senha incorreta para email - $email");
                }
            }
        } catch (PDOException $e) {
            $error = 'Erro ao conectar com o banco de dados: ' . $e->getMessage();
            error_log("Erro de banco de dados no login: " . $e->getMessage());
        } catch (Exception $e) {
            $error = 'Erro interno: ' . $e->getMessage();
            error_log("Erro geral no login: " . $e->getMessage());
        }
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(120deg, #baf3d7, #c2f5de, #cbf7e4, #d4f8ea, #ddfaef);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .login-header i {
            font-size: 3rem;
            color: #009d63;
            margin-bottom: 1rem;
        }
        .login-header h1 {
            font-size: 1.75rem;
            color: #333;
            margin-bottom: 0.5rem;
        }
        .login-header p {
            color: #666;
            margin-bottom: 0;
        }
        .form-control {
            padding: 0.75rem 1rem;
            border-radius: 8px;
        }
        .btn-login {
            background: #009d63;
            border: none;
            padding: 0.75rem;
            border-radius: 8px;
            font-weight: 500;
            width: 100%;
            margin-top: 1rem;
            color: white;
        }
        .btn-login:hover {
            background: #008d53;
            color: white;
        }
        .alert {
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        .form-group label {
            margin-bottom: 0.5rem;
            color: #555;
        }
        .password-toggle {
            cursor: pointer;
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <i class="fas fa-store"></i>
            <h1>Painel Admin</h1>
            <p>Sistema de Vendas</p>
        </div>
        
        <?php if ($error): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>
        
        <form method="POST" action="">
            <div class="form-group">
                <label for="email">Email</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-envelope"></i>
                    </span>
                    <input type="email" 
                           class="form-control" 
                           id="email" 
                           name="email" 
                           required 
                           placeholder="Seu email"
                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                </div>
            </div>
            
            <div class="form-group">
                <label for="password">Senha</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-lock"></i>
                    </span>
                    <input type="password" 
                           class="form-control" 
                           id="password" 
                           name="password" 
                           required 
                           placeholder="Sua senha">
                    <span class="input-group-text password-toggle" onclick="togglePassword()">
                        <i class="fas fa-eye"></i>
                    </span>
                </div>
            </div>
            
            <button type="submit" class="btn btn-login">
                <i class="fas fa-sign-in-alt me-2"></i>
                Entrar
            </button>
        </form>
    </div>
    
    <script>
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.querySelector('.password-toggle i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
    </script>
</body>
</html>
