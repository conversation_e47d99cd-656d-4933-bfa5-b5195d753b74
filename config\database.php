<?php
if (!class_exists('Database')) {
    class Database {
        private static $instance = null;
        private $conn;
        
        private function __construct() {
            // Carregar variáveis de ambiente
            require_once __DIR__ . '/../includes/env_loader.php';

            $host = EnvLoader::get('DB_HOST', 'localhost');
            $db   = EnvLoader::get('DB_NAME', 'u276254152_banco_loja');
            $user = EnvLoader::get('DB_USER', 'root');
            $pass = EnvLoader::get('DB_PASS', '');
            $charset = 'utf8mb4';
            
            $dsn = "mysql:host=$host;dbname=$db;charset=$charset";
            $options = [
                PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES   => false,
            ];
            
            try {
                $this->conn = new PDO($dsn, $user, $pass, $options);
            } catch (PDOException $e) {
                die("Erro na conexão: " . $e->getMessage());
            }
        }
        
        public static function getInstance() {
            if (self::$instance === null) {
                self::$instance = new self();
            }
            return self::$instance;
        }
        
        public function getConnection() {
            return $this->conn;
        }
        
        // Prevent cloning of the instance
        private function __clone() {}
        
        // Prevent unserializing of the instance
        public function __wakeup() {
            throw new Exception("Cannot unserialize singleton");
        }
    }
}
?>

