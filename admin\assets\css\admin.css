/* ========== CSS ADMIN COMPLEMENTAR ========== */
/* Este arquivo complementa o sistema padronizado */

:root {
    /* Mantém compatibilidade com variáveis existentes */
    --main-bg-color: #f8f9fa;
    --admin-primary: #198754;
    --admin-secondary: #6c757d;
    --admin-accent: #157347;
}

/* ========== LAYOUT WRAPPER ========== */

#wrapper {
    overflow-x: hidden !important;
    background-color: var(--main-bg-color) !important;
    min-height: 100vh !important;
}

/* ========== SIDEBAR ESPECÍFICO ========== */

#sidebar-wrapper {
    min-height: 100vh !important;
    width: 250px !important;
    transition: margin 0.25s ease-out !important;
    background: white !important;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1) !important;
    border-right: 1px solid #e2e8f0 !important;
}

#sidebar-wrapper .sidebar-heading {
    padding: 20px 16px !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #212529 !important;
    border-bottom: 1px solid #e2e8f0 !important;
    background: #f8f9fa !important;
    text-align: center !important;
}

#sidebar-wrapper .list-group {
    width: 100% !important;
    border: none !important;
}

#page-content-wrapper {
    flex: 1 !important;
    min-width: 0 !important;
    padding: 0 !important;
}

/* ========== ESTADOS DO MENU ========== */

#wrapper.toggled #sidebar-wrapper {
    margin-left: -250px !important;
}

/* ========== CLASSES UTILITÁRIAS ========== */

.primary-text {
    color: var(--admin-primary) !important;
}

.second-text {
    color: var(--admin-secondary) !important;
}

.secondary-bg {
    background-color: var(--admin-secondary) !important;
}

.rounded-full {
    border-radius: 50% !important;
}

#menu-toggle {
    cursor: pointer !important;
    transition: all 0.3s ease !important;
}

#menu-toggle:hover {
    background-color: var(--admin-primary) !important;
    border-color: var(--admin-primary) !important;
}

/* ========== ITENS DO MENU ========== */

.list-group-item {
    border: none !important;
    padding: 12px 20px !important;
    height: 50px !important;
    display: flex !important;
    align-items: center !important;
    transition: all 0.3s ease !important;
    color: #212529 !important;
    text-decoration: none !important;
    background: transparent !important;
}

.list-group-item:hover {
    background-color: rgba(25, 135, 84, 0.1) !important;
    color: var(--admin-primary) !important;
}

.list-group-item.active {
    background-color: var(--admin-primary) !important;
    color: white !important;
    font-weight: 600 !important;
    border: none !important;
}

.list-group-item i {
    margin-right: 12px !important;
    width: 20px !important;
    text-align: center !important;
    font-size: 16px !important;
    color: var(--admin-secondary) !important;
}

.list-group-item:hover i {
    color: var(--admin-primary) !important;
}

.list-group-item.active i {
    color: white !important;
}

/* ========== RESPONSIVIDADE ========== */

@media (min-width: 992px) {
    #sidebar-wrapper {
        margin-left: 0 !important;
    }

    #page-content-wrapper {
        min-width: 0 !important;
        width: 100% !important;
    }

    #wrapper.toggled #sidebar-wrapper {
        margin-left: -250px !important;
    }
}

@media (max-width: 991.98px) {
    #sidebar-wrapper {
        margin-left: -250px !important;
    }

    #wrapper.toggled #sidebar-wrapper {
        margin-left: 0 !important;
    }
}

/* ========== CARDS E TABELAS ========== */

.card-admin {
    border: none !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
    background: white !important;
    margin-bottom: 20px !important;
    transition: all 0.3s ease !important;
}

.card-admin:hover {
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15) !important;
    transform: translateY(-2px) !important;
}

.table-admin {
    border-radius: 8px !important;
    overflow: hidden !important;
    background: white !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* ========== FIM DO CSS ADMIN COMPLEMENTAR ========== */