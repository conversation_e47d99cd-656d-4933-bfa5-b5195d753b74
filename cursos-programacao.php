<?php
/**
 * Página SEO: Cursos de Programação - KESUNG SITE
 * Otimizada para ranquear em "curso programação web", "aprender programação"
 */

// Conexão com banco usando .env
require_once __DIR__ . '/includes/env_loader.php';

try {
    $pdo = new PDO(
        "mysql:host=" . EnvLoader::get('DB_HOST', 'localhost') . ";dbname=" . EnvLoader::get('DB_NAME', 'u276254152_banco_loja') . ";charset=utf8mb4",
        EnvLoader::get('DB_USER', 'root'),
        EnvLoader::get('DB_PASS', ''),
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    // Buscar cursos de programação
    $stmt = $pdo->query("
        SELECT * FROM products 
        WHERE status = 'active' 
        AND (name LIKE '%curso%' OR name LIKE '%programação%' OR name LIKE '%site%')
        ORDER BY created_at DESC
    ");
    $cursos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $cursos = [];
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- SEO Meta Tags -->
    <title>Cursos de Programação Web 2024 - Aprenda HTML, CSS, JavaScript e PHP | KESUNG SITE</title>
    <meta name="description" content="Cursos completos de programação web para iniciantes e avançados. Aprenda HTML, CSS, JavaScript, PHP e crie sites profissionais. Certificado incluso.">
    <meta name="keywords" content="curso programação web, aprender programação, curso html css, curso javascript, curso php, criar site profissional, programação para iniciantes, desenvolvimento web, curso online programação">
    
    <!-- Open Graph -->
    <meta property="og:title" content="Cursos de Programação Web 2024 - KESUNG SITE">
    <meta property="og:description" content="Cursos completos de programação web para iniciantes e avançados. Aprenda HTML, CSS, JavaScript, PHP e crie sites profissionais.">
    <meta property="og:image" content="https://kesungsite.com/assets/images/cursos-programacao-og.jpg">
    <meta property="og:url" content="https://kesungsite.com/cursos-programacao">
    
    <!-- Schema.org -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Course",
        "name": "Cursos de Programação Web",
        "description": "Cursos completos de programação web para iniciantes e avançados",
        "provider": {
            "@type": "Organization",
            "name": "KESUNG SITE",
            "url": "https://kesungsite.com"
        },
        "courseMode": "online",
        "educationalLevel": "Beginner to Advanced",
        "inLanguage": "pt-BR"
    }
    </script>
    
    <link rel="canonical" href="https://kesungsite.com/cursos-programacao">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <header class="bg-primary text-white py-4">
        <div class="container">
            <nav class="d-flex justify-content-between align-items-center">
                <a href="/" class="text-white text-decoration-none">
                    <h1 class="h3 mb-0">KESUNG SITE</h1>
                </a>
                <div>
                    <a href="/" class="text-white me-3">Início</a>
                    <a href="/scripts-php" class="text-white me-3">Scripts PHP</a>
                    <a href="/casa-apostas" class="text-white me-3">Casa de Apostas</a>
                    <a href="/marketing-digital" class="text-white">Marketing Digital</a>
                </div>
            </nav>
        </div>
    </header>

    <main class="container my-5">
        <!-- Hero Section -->
        <section class="text-center mb-5">
            <h1 class="display-4 fw-bold text-primary mb-3">
                Cursos de Programação Web 2024
            </h1>
            <p class="lead text-muted mb-4">
                Aprenda a criar sites profissionais do zero com nossos cursos completos de HTML, CSS, JavaScript e PHP. 
                Ideal para iniciantes e profissionais que querem se especializar.
            </p>
            <div class="row text-center">
                <div class="col-md-3">
                    <i class="fas fa-code fa-3x text-primary mb-2"></i>
                    <h5>Código Prático</h5>
                    <p class="small">Aprenda fazendo projetos reais</p>
                </div>
                <div class="col-md-3">
                    <i class="fas fa-certificate fa-3x text-success mb-2"></i>
                    <h5>Certificado</h5>
                    <p class="small">Certificado de conclusão incluso</p>
                </div>
                <div class="col-md-3">
                    <i class="fas fa-headset fa-3x text-info mb-2"></i>
                    <h5>Suporte</h5>
                    <p class="small">Suporte direto com instrutor</p>
                </div>
                <div class="col-md-3">
                    <i class="fas fa-infinity fa-3x text-warning mb-2"></i>
                    <h5>Acesso Vitalício</h5>
                    <p class="small">Acesso para sempre</p>
                </div>
            </div>
        </section>

        <!-- Cursos Disponíveis -->
        <section class="mb-5">
            <h2 class="h3 mb-4">Nossos Cursos de Programação</h2>
            <div class="row">
                <?php foreach ($cursos as $curso): ?>
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <?php if (!empty($curso['image'])): ?>
                        <img src="uploads/products/<?php echo htmlspecialchars($curso['image']); ?>" 
                             class="card-img-top" alt="<?php echo htmlspecialchars($curso['name']); ?>"
                             style="height: 200px; object-fit: cover;">
                        <?php endif; ?>
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title"><?php echo htmlspecialchars($curso['name']); ?></h5>
                            <p class="card-text flex-grow-1"><?php echo htmlspecialchars($curso['description']); ?></p>
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span class="h4 text-primary mb-0">
                                        R$ <?php echo number_format($curso['price'], 2, ',', '.'); ?>
                                    </span>
                                    <small class="text-muted">
                                        <i class="fas fa-star text-warning"></i> 4.9/5
                                    </small>
                                </div>
                                <button class="btn btn-primary w-100" onclick="buyProduct(<?php echo $curso['id']; ?>)">
                                    <i class="fas fa-shopping-cart me-2"></i>Comprar Agora
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </section>

        <!-- SEO Content -->
        <section class="mb-5">
            <div class="row">
                <div class="col-lg-8">
                    <h2>Por que Aprender Programação Web em 2024?</h2>
                    <p>A programação web é uma das habilidades mais valorizadas no mercado atual. Com nossos cursos, você aprenderá:</p>
                    
                    <h3>HTML5 e CSS3 Moderno</h3>
                    <p>Domine a estrutura e estilização de páginas web com as tecnologias mais atuais. Aprenda a criar layouts responsivos que funcionam em qualquer dispositivo.</p>
                    
                    <h3>JavaScript Interativo</h3>
                    <p>Adicione interatividade aos seus sites com JavaScript. Desde animações simples até aplicações complexas, você dominará a linguagem mais popular da web.</p>
                    
                    <h3>PHP e Banco de Dados</h3>
                    <p>Crie sistemas dinâmicos com PHP e MySQL. Aprenda a desenvolver sites que interagem com bancos de dados e processam informações em tempo real.</p>
                    
                    <h3>Projetos Práticos</h3>
                    <p>Todos os nossos cursos incluem projetos práticos que você pode adicionar ao seu portfólio. Saia do curso com experiência real de desenvolvimento.</p>
                </div>
                <div class="col-lg-4">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h5>O que você vai aprender:</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>HTML5 semântico</li>
                                <li><i class="fas fa-check text-success me-2"></i>CSS3 e Flexbox/Grid</li>
                                <li><i class="fas fa-check text-success me-2"></i>JavaScript ES6+</li>
                                <li><i class="fas fa-check text-success me-2"></i>PHP orientado a objetos</li>
                                <li><i class="fas fa-check text-success me-2"></i>MySQL e PDO</li>
                                <li><i class="fas fa-check text-success me-2"></i>Bootstrap e frameworks</li>
                                <li><i class="fas fa-check text-success me-2"></i>Git e versionamento</li>
                                <li><i class="fas fa-check text-success me-2"></i>Deploy e hospedagem</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ -->
        <section class="mb-5">
            <h2>Perguntas Frequentes</h2>
            <div class="accordion" id="faqAccordion">
                <div class="accordion-item">
                    <h3 class="accordion-header">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                            Preciso ter conhecimento prévio em programação?
                        </button>
                    </h3>
                    <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            Não! Nossos cursos são desenvolvidos para iniciantes completos. Começamos do básico e evoluímos gradualmente.
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <h3 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                            Quanto tempo leva para concluir um curso?
                        </button>
                    </h3>
                    <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            Depende do seu ritmo de estudo. Em média, dedicando 2-3 horas por dia, você conclui em 2-3 meses.
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="bg-dark text-white py-4">
        <div class="container text-center">
            <p>&copy; 2024 KESUNG SITE. Todos os direitos reservados.</p>
            <div>
                <a href="/termos-uso" class="text-white me-3">Termos de Uso</a>
                <a href="/politica-privacidade" class="text-white">Política de Privacidade</a>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/simple_pix.js"></script>
</body>
</html>
