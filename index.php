<?php
/**
 * Index Principal - Sistema kesung-site
 * Versão corrigida e padronizada
 */

error_reporting(E_ALL);
ini_set('display_errors', 0); // Desabilitar em produção

try {
    // Carregar variáveis de ambiente
    require_once __DIR__ . '/includes/env_loader.php';

    // Conexão usando .env
    $pdo = new PDO(
        "mysql:host=" . EnvLoader::get('DB_HOST', 'localhost') . ";dbname=" . EnvLoader::get('DB_NAME', 'u276254152_banco_loja') . ";charset=utf8mb4",
        EnvLoader::get('DB_USER', 'root'),
        EnvLoader::get('DB_PASS', ''),
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    // Buscar configurações do site (com fallback)
    $settings = [];
    try {
        $stmt = $pdo->query("SELECT * FROM site_settings LIMIT 1");
        $settings = $stmt->fetch() ?: [];
    } catch (PDOException $e) {
        // Valores padrão se tabela não existir
        $settings = [
            'title' => 'Kesung Site',
            'tab_title' => 'KESUNG SITE - VENDAS',
            'description' => 'Sistema de vendas digital',
            'footer_text' => '© 2024 Kesung Site',
            'logo_path' => null,
            'favicon_path' => null,
            'whatsapp_number' => null,
            'facebook_url' => null,
            'instagram_url' => null,
            'youtube_url' => null,
            'analytics_code' => null,
            'show_logo' => 1
        ];
    }

    // Buscar produtos (com fallback para tabelas opcionais)
    $produtos = [];
    try {
        // Query simplificada sem joins complexos
        $stmt = $pdo->query("
            SELECT p.*
            FROM products p
            WHERE p.status = 'active'
            ORDER BY p.created_at DESC
        ");
        $produtos = $stmt->fetchAll();

        // Adicionar dados extras se as tabelas existirem
        foreach ($produtos as &$produto) {
            // Total de vendas (se tabela sales existir)
            try {
                $stmt = $pdo->prepare("SELECT COUNT(*) as total_sales FROM sales WHERE product_id = ?");
                $stmt->execute([$produto['id']]);
                $produto['total_sales'] = $stmt->fetchColumn();
            } catch (PDOException $e) {
                $produto['total_sales'] = 0;
            }

            // Avaliação média (se tabela product_ratings existir)
            try {
                $stmt = $pdo->prepare("SELECT AVG(rating) as avg_rating FROM product_ratings WHERE product_id = ?");
                $stmt->execute([$produto['id']]);
                $produto['avg_rating'] = $stmt->fetchColumn();
            } catch (PDOException $e) {
                $produto['avg_rating'] = null;
            }
        }

    } catch (PDOException $e) {
        // Se tabela products não existir, array vazio
        $produtos = [];
    }

    // Log de visitante (opcional, sem quebrar se falhar)
    try {
        if (file_exists('includes/visitor_tracker.php')) {
            require_once 'includes/visitor_tracker.php';
        } elseif (file_exists('includes/visitor_counter.php')) {
            require_once 'includes/visitor_counter.php';
            logVisitor();
        }
    } catch (Exception $e) {
        // Ignorar erros de visitor counter
        error_log("Visitor counter error: " . $e->getMessage());
    }

} catch (PDOException $e) {
    // Erro crítico de conexão
    die("
    <div style='text-align: center; padding: 50px; font-family: Arial, sans-serif;'>
        <h2 style='color: #dc3545;'>Erro de Conexão</h2>
        <p>Não foi possível conectar ao banco de dados.</p>
        <p><a href='test_connection.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Testar Conexão</a></p>
    </div>
    ");
}
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($settings['tab_title'] ?? 'KESUNG SITE - Cursos de Programação e Scripts PHP Profissionais'); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($settings['description'] ?? 'Cursos de programação web, scripts PHP para casa de apostas, Facebook Ads e sistemas completos. Aprenda a criar sites profissionais e monetize seus conhecimentos.'); ?>">

    <!-- Meta Tags SEO Avançadas -->
    <meta name="keywords" content="curso programação web, script php casa apostas, facebook ads, criar site profissional, curso html css javascript, sistema apostas esportivas, script php completo, marketing digital, programação para iniciantes, desenvolvimento web">
    <meta name="author" content="KESUNG SITE">
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <meta name="googlebot" content="index, follow">
    <meta name="bingbot" content="index, follow">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://kesungsite.com/">
    <meta property="og:title" content="KESUNG SITE - Cursos de Programação e Scripts PHP Profissionais">
    <meta property="og:description" content="Cursos de programação web, scripts PHP para casa de apostas, Facebook Ads e sistemas completos. Aprenda a criar sites profissionais e monetize seus conhecimentos.">
    <meta property="og:image" content="https://kesungsite.com/assets/images/og-image.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:site_name" content="KESUNG SITE">
    <meta property="og:locale" content="pt_BR">

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://kesungsite.com/">
    <meta name="twitter:title" content="KESUNG SITE - Cursos de Programação e Scripts PHP Profissionais">
    <meta name="twitter:description" content="Cursos de programação web, scripts PHP para casa de apostas, Facebook Ads e sistemas completos.">
    <meta name="twitter:image" content="https://kesungsite.com/assets/images/twitter-card.jpg">

    <!-- Schema.org Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "KESUNG SITE",
        "url": "https://kesungsite.com",
        "logo": "https://kesungsite.com/assets/images/logo.png",
        "description": "Cursos de programação web, scripts PHP para casa de apostas, Facebook Ads e sistemas completos",
        "sameAs": [
            "https://www.facebook.com/kesungsite",
            "https://www.instagram.com/kesungsite",
            "https://www.youtube.com/kesungsite"
        ],
        "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "+55-11-99999-9999",
            "contactType": "customer service",
            "availableLanguage": "Portuguese"
        }
    }
    </script>

    <!-- Canonical URL -->
    <link rel="canonical" href="https://kesungsite.com/">

    <!-- Preconnect para performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="preconnect" href="https://cdn.jsdelivr.net">

    <!-- DNS Prefetch -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="//cdn.jsdelivr.net">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#4318FF">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="KESUNG SITE">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="application-name" content="KESUNG SITE">

    <!-- PWA Icons -->
    <link rel="manifest" href="/manifest.json">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon.png">
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">
    <link rel="icon" type="image/svg+xml" sizes="192x192" href="/assets/icons/icon-192x192.svg">

    <?php if (!empty($settings['favicon_path'])): ?>
    <link rel="icon" type="image/x-icon" href="uploads/<?php echo htmlspecialchars($settings['favicon_path']); ?>">
    <?php endif; ?>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    <link rel="stylesheet" href="assets/css/chat.css">
    <link rel="stylesheet" href="assets/css/dynamic-styles.php">
    <?php if (!empty($settings['analytics_code'])): ?>
        <?php echo $settings['analytics_code']; ?>
    <?php endif; ?>
</head>
<body>
    <!-- PWA Install Banner Compacto -->
    <div id="pwa-install-banner" class="pwa-install-banner-compact" style="display: none;">
        <div class="pwa-banner-content-compact">
            <div class="pwa-banner-info">
                <i class="fas fa-mobile-alt pwa-icon"></i>
                <span class="pwa-text">Instale nosso app</span>
            </div>
            <div class="pwa-banner-actions">
                <button onclick="installPWAApp()" class="pwa-install-btn-compact">
                    <i class="fas fa-download"></i>
                    Instalar
                </button>
                <button onclick="dismissPWABanner()" class="pwa-close-btn-compact">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>





    <header class="header">
        <div class="container">
            <?php if (!empty($settings['logo_path']) && (!isset($settings['show_logo']) || $settings['show_logo'])): ?>
                <img src="uploads/<?php echo htmlspecialchars($settings['logo_path']); ?>"
                     alt="<?php echo htmlspecialchars($settings['title']); ?>"
                     class="img-fluid" style="max-height: 40px; margin-right: 15px;">
            <?php endif; ?>
            <h1><?php echo htmlspecialchars($settings['title'] ?? 'KESUNG SITE'); ?></h1>
        </div>
    </header>



    <div class="container">
        <?php if (empty($produtos)): ?>
            <div class="alert alert-info text-center" style="margin: 2rem auto; max-width: 600px; padding: 3rem; border-radius: 15px; background: white; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                <h3>Nenhum produto disponível</h3>
                <p class="text-muted">Em breve teremos produtos incríveis para você!</p>
                <a href="test_connection.php" class="btn btn-primary">
                    <i class="fas fa-tools me-2"></i>Testar Sistema
                </a>
                <a href="admin/" class="btn btn-success ms-2">
                    <i class="fas fa-cog me-2"></i>Área Admin
                </a>
            </div>
        <?php else: ?>
            <?php foreach ($produtos as $produto): ?>
                <div class="card">
                    <?php if (!empty($produto['image'])): ?>
                        <img src="uploads/products/<?php echo htmlspecialchars($produto['image']); ?>"
                             alt="<?php echo htmlspecialchars($produto['name']); ?>"
                             class="card-image"
                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjhmOWZhIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZjNzU3ZCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlbTwvdGV4dD48L3N2Zz4='">
                    <?php else: ?>
                        <div class="card-image d-flex align-items-center justify-content-center bg-light">
                            <i class="fas fa-image fa-3x text-muted"></i>
                        </div>
                    <?php endif; ?>
                    <div class="card-content">
                        <h2 class="card-title"><?php echo htmlspecialchars($produto['name']); ?></h2>
                        <p class="card-description"><?php echo htmlspecialchars($produto['description'] ?? ''); ?></p>

                        <div class="card-price">
                            <span class="current-price">R$ <?php echo number_format($produto['price'], 2, ',', '.'); ?></span>
                        </div>

                        <button class="btn btn-primary btn-block w-100 buy-button"
                                onclick="buyProduct(<?php echo $produto['id']; ?>)">
                            <i class="fas fa-shopping-cart"></i> COMPRAR AGORA
                        </button>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <div id="chatButton">
        <button id="openChat">
            <i class="fas fa-comments"></i>
            <span>Chat de Atendimento</span>
        </button>
    </div>

    <div class="cookie-banner" id="cookieBanner">
        <p>Utilizamos cookies para melhorar sua experiência. Ao continuar navegando, você concorda com nossa <a href="#" onclick="openPrivacyPolicy()">Política de Privacidade</a>.</p>
        <div class="buttons">
            <button id="accept-cookies">Aceitar</button>
            <button id="decline-cookies">Recusar</button>
        </div>
    </div>

    <footer class="footer text-center">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <?php if (!empty($settings['footer_text'])): ?>
                        <p><?php echo htmlspecialchars($settings['footer_text']); ?></p>
                    <?php endif; ?>
                    <div class="social-links">
                        <?php if (!empty($settings['whatsapp_number'])): ?>
                            <a href="#" onclick="openChat(); return false;">
                                <i class="fab fa-whatsapp"></i>
                            </a>
                        <?php endif; ?>
                        <?php if (!empty($settings['facebook_url'])): ?>
                            <a href="<?php echo htmlspecialchars($settings['facebook_url']); ?>" target="_blank">
                                <i class="fab fa-facebook"></i>
                            </a>
                        <?php endif; ?>
                        <?php if (!empty($settings['instagram_url'])): ?>
                            <a href="<?php echo htmlspecialchars($settings['instagram_url']); ?>" target="_blank">
                                <i class="fab fa-instagram"></i>
                            </a>
                        <?php endif; ?>
                        <?php if (!empty($settings['youtube_url'])): ?>
                            <a href="<?php echo htmlspecialchars($settings['youtube_url']); ?>" target="_blank">
                                <i class="fab fa-youtube"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </footer>

<!-- Modal para imagem -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <img id="modalImage" src="" alt="Imagem Ampliada">
            </div>
            <div class="modal-footer">
                <small class="text-muted">&copy; 2024 Sistema de Vendas. Todos os direitos reservados.</small>
            </div>
        </div>
    </div>
</div>
    <!-- Modais removidos - usando SweetAlert2 -->

    <!-- Modal da Política de Privacidade -->
    <div class="modal fade" id="privacyPolicyModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Política de Privacidade</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <h6>1. Coleta de Informações</h6>
                    <p>Coletamos informações quando você se registra em nosso site, faz uma compra ou preenche um formulário. As informações coletadas incluem seu nome, e-mail, endereço e/ou telefone.</p>

                    <h6>2. Uso das Informações</h6>
                    <p>As informações que coletamos são utilizadas para:</p>
                    <ul>
                        <li>Personalizar sua experiência</li>
                        <li>Melhorar nosso site</li>
                        <p>3. Proteção das Informações</p>
                        <p>Implementamos diversas medidas de segurança para manter a segurança de suas informações pessoais. Utilizamos criptografia avançada para proteger informações sensíveis transmitidas online.</p>

                        <h6>4. Cookies</h6>
                        <p>Utilizamos cookies para melhorar o acesso e personalizar a experiência no nosso site. Cookies são pequenos arquivos que um site transfere para o disco rígido do seu computador através do navegador (se você permitir).</p>

                        <h6>5. Divulgação para Terceiros</h6>
                        <p>Não vendemos, comercializamos ou transferimos para terceiros suas informações pessoais identificáveis. Isso não inclui terceiros confiáveis que nos auxiliam na operação do site, condução dos negócios ou em seu atendimento.</p>

                        <h6>6. Consentimento</h6>
                        <p>Ao usar nosso site, você concorda com nossa política de privacidade.</p>

                        <h6>7. Alterações</h6>
                        <p>Quaisquer alterações em nossas políticas de privacidade serão publicadas nesta página.</p>

                        <h6>8. Contato</h6>
                        <p>Se você tiver alguma dúvida sobre esta política de privacidade, entre em contato conosco através do nosso formulário de contato.</p>
                    </div>
                </div>
                <!--<div class="modal-footer">-->
                <!--    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>-->
                <!--</div>-->
            </div>
        </div>
    </div>

    <!-- Notificação -->
    <div id="notification" class="notification"></div>

    <style>
    /* PWA Install Banner Compacto */
    .pwa-install-banner-compact {
        position: fixed;
        top: 10px;
        right: 10px;
        background: linear-gradient(135deg, #4318FF, #9333EA);
        color: white;
        z-index: 9999;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(67, 24, 255, 0.3);
        animation: slideInRight 0.4s ease-out;
        max-width: 280px;
        width: auto;
    }

    .pwa-banner-content-compact {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        gap: 12px;
    }

    .pwa-banner-info {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;
    }

    .pwa-icon {
        font-size: 18px;
        color: white;
        flex-shrink: 0;
    }

    .pwa-text {
        font-size: 14px;
        font-weight: 500;
        color: white;
        white-space: nowrap;
    }

    .pwa-banner-actions {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .pwa-install-btn-compact {
        background: rgba(255,255,255,0.2);
        color: white;
        border: 1px solid rgba(255,255,255,0.3);
        padding: 6px 12px;
        font-size: 12px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .pwa-install-btn-compact:hover {
        background: rgba(255,255,255,0.3);
        color: white;
        transform: translateY(-1px);
    }

    .pwa-close-btn-compact {
        background: none;
        border: none;
        color: white;
        font-size: 14px;
        cursor: pointer;
        padding: 4px;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all 0.2s ease;
        opacity: 0.8;
    }

    .pwa-close-btn-compact:hover {
        background: rgba(255,255,255,0.2);
        color: white;
        opacity: 1;
    }

    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    /* Responsividade Mobile para Banner Compacto */
    @media (max-width: 768px) {
        .pwa-install-banner-compact {
            top: 10px;
            right: 10px;
            left: 10px;
            max-width: none;
            border-radius: 10px;
        }

        .pwa-banner-content-compact {
            padding: 10px 12px;
            gap: 10px;
        }

        .pwa-text {
            font-size: 13px;
        }

        .pwa-install-btn-compact {
            font-size: 11px;
            padding: 5px 10px;
        }

        .pwa-icon {
            font-size: 16px;
        }
    }

    @media (max-width: 480px) {
        .pwa-install-banner-compact {
            top: 5px;
            right: 5px;
            left: 5px;
        }

        .pwa-banner-content-compact {
            padding: 8px 10px;
            gap: 8px;
        }

        .pwa-text {
            font-size: 12px;
        }

        .pwa-install-btn-compact {
            font-size: 10px;
            padding: 4px 8px;
        }

        .pwa-icon {
            font-size: 14px;
        }

        .pwa-close-btn-compact {
            width: 20px;
            height: 20px;
            font-size: 12px;
        }
    }

    /* Barra de Progresso de Instalação */
    .pwa-progress-container {
        width: 80px;
        height: 4px;
        background: rgba(255,255,255,0.3);
        border-radius: 2px;
        overflow: hidden;
    }

    .pwa-progress-bar {
        width: 100%;
        height: 100%;
        position: relative;
    }

    .pwa-progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #fff, #e0e0e0);
        border-radius: 2px;
        width: 30%;
        transition: width 0.8s ease;
        animation: progressPulse 1.5s infinite;
    }

    @keyframes progressPulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
    }

    /* Banner de Sucesso */
    .pwa-success-banner {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        z-index: 10000;
        border-radius: 15px;
        box-shadow: 0 8px 30px rgba(16, 185, 129, 0.4);
        animation: successSlideIn 0.5s ease-out;
        padding: 20px 30px;
        text-align: center;
        min-width: 280px;
    }

    .pwa-success-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
    }

    .pwa-success-icon {
        font-size: 32px;
        color: white;
        margin-bottom: 5px;
    }

    .pwa-success-text {
        font-size: 16px;
        font-weight: 600;
        color: white;
    }

    .pwa-success-subtitle {
        font-size: 12px;
        color: rgba(255,255,255,0.9);
        margin-top: 5px;
    }

    @keyframes successSlideIn {
        from {
            transform: translate(-50%, -50%) scale(0.8);
            opacity: 0;
        }
        to {
            transform: translate(-50%, -50%) scale(1);
            opacity: 1;
        }
    }

    /* Estilos do Modal PIX - Responsivo */
    #pixModal .modal-content {
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    }

    #pixModal .modal-header {
        border-radius: 15px 15px 0 0;
        border-bottom: none;
    }

    .pix-amount-display {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 2px solid #dee2e6;
    }

    .qr-code-container {
        background: #fff;
        border: 3px solid #e9ecef;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .instruction-step {
        background: #f8f9fa;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .instruction-step:hover {
        background: #e9ecef;
        transform: translateY(-2px);
    }

    .step-number {
        width: 30px;
        height: 30px;
        background: #4318FF;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin: 0 auto 8px;
        font-size: 14px;
    }

    .success-icon {
        animation: successPulse 1s ease-in-out;
    }

    @keyframes successPulse {
        0% { transform: scale(0.5); opacity: 0; }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); opacity: 1; }
    }

    /* Loading Modal */
    .loading-modal .modal-content {
        border: none;
        border-radius: 15px;
    }

    .loading-spinner {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 20px auto;
    }

    .spinner-ring {
        width: 80px;
        height: 80px;
        border: 6px solid #f3f3f3;
        border-top: 6px solid #4318FF;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Responsividade Mobile */
    @media (max-width: 768px) {
        #pixModal .modal-dialog {
            margin: 0.5rem;
            max-width: calc(100% - 1rem);
        }

        .qr-code-container img {
            width: 200px !important;
            height: 200px !important;
        }

        .instruction-step {
            padding: 0.5rem !important;
        }

        .step-number {
            width: 25px;
            height: 25px;
            font-size: 12px;
        }

        #pixModal .modal-body {
            padding: 1rem !important;
        }
    }

    @media (max-width: 480px) {
        .qr-code-container img {
            width: 180px !important;
            height: 180px !important;
        }

        .pix-amount-display h3 {
            font-size: 1.5rem !important;
        }
    }
    </style>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/js/simple_pix.js"></script>
    <script src="assets/js/chat.js"></script>
    <script src="assets/sounds/notification.js"></script>
    <script src="assets/js/pwa.js"></script>
    <script>
        // PWA Install Banner Management
        let deferredPrompt;
        let pwaManagerInstance;

        // Detectar se é mobile
        function isMobileDevice() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        }

        // Verificar se já está instalado
        function isPWAInstalled() {
            return window.matchMedia('(display-mode: standalone)').matches ||
                   window.navigator.standalone === true;
        }

        // Mostrar banner de instalação
        function showPWABanner() {
            const banner = document.getElementById('pwa-install-banner');
            if (banner) {
                banner.style.display = 'block';
                console.log('🧪 TESTE: Banner PWA forçado a aparecer');
            }
        }

        // Função de teste para simular fluxo de instalação
        function testInstallFlow() {
            console.log('🧪 TESTE: Simulando fluxo de instalação');

            // Mostrar banner
            showPWABanner();

            // Simular clique após 2 segundos
            setTimeout(() => {
                console.log('🧪 TESTE: Simulando clique no botão instalar');

                if (deferredPrompt) {
                    // Se há prompt real, usar
                    installPWAApp();
                } else {
                    // Simular instalação para teste
                    simulateInstallation();
                }
            }, 2000);
        }

        // Simular instalação para teste
        function simulateInstallation() {
            console.log('🧪 TESTE: Simulando instalação (sem prompt real)');

            // Mostrar progresso
            showInstallProgress();

            setTimeout(() => {
                updateInstallProgress('installing');
                setTimeout(() => {
                    updateInstallProgress('complete');
                    setTimeout(() => {
                        hideInstallProgress();
                        showSuccessMessage();
                        console.log('🧪 TESTE: Simulação de instalação concluída');
                    }, 1000);
                }, 2000);
            }, 1000);
        }

        // Instalar PWA com progresso
        async function installPWAApp() {
            if (deferredPrompt) {
                // Mostrar progresso de instalação
                showInstallProgress();

                try {
                    deferredPrompt.prompt();
                    const { outcome } = await deferredPrompt.userChoice;
                    console.log('PWA install result:', outcome);

                    if (outcome === 'accepted') {
                        // Atualizar progresso para instalando
                        updateInstallProgress('installing');

                        // Aguardar um pouco para simular instalação
                        await new Promise(resolve => setTimeout(resolve, 2000));

                        // Finalizar instalação
                        updateInstallProgress('complete');

                        // Aguardar mais um pouco antes de abrir
                        await new Promise(resolve => setTimeout(resolve, 1000));

                        // Fechar banner e abrir app
                        dismissPWABanner();
                        hideInstallProgress();

                        // Rastrear instalação
                        trackPWAInstall();

                        // Tentar abrir o app instalado
                        openInstalledApp();

                    } else {
                        // Usuário cancelou
                        hideInstallProgress();
                    }
                } catch (error) {
                    console.error('Erro na instalação:', error);
                    hideInstallProgress();
                }

                deferredPrompt = null;
            }
        }

        // Dispensar banner
        function dismissPWABanner() {
            const banner = document.getElementById('pwa-install-banner');
            if (banner) {
                banner.style.display = 'none';
                // Salvar preferência para não mostrar novamente por 7 dias
                localStorage.setItem('pwa-banner-dismissed', Date.now() + (7 * 24 * 60 * 60 * 1000));
            }
        }

        // Mostrar progresso de instalação
        function showInstallProgress() {
            const banner = document.getElementById('pwa-install-banner');
            if (banner) {
                banner.innerHTML = `
                    <div class="pwa-banner-content-compact">
                        <div class="pwa-banner-info">
                            <i class="fas fa-mobile-alt pwa-icon"></i>
                            <span class="pwa-text">Preparando instalação...</span>
                        </div>
                        <div class="pwa-progress-container">
                            <div class="pwa-progress-bar">
                                <div class="pwa-progress-fill" id="pwa-progress-fill"></div>
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        // Atualizar progresso de instalação
        function updateInstallProgress(stage) {
            const progressFill = document.getElementById('pwa-progress-fill');
            const textElement = document.querySelector('.pwa-text');

            if (stage === 'installing') {
                if (progressFill) progressFill.style.width = '70%';
                if (textElement) textElement.textContent = 'Instalando app...';
            } else if (stage === 'complete') {
                if (progressFill) progressFill.style.width = '100%';
                if (textElement) textElement.textContent = 'Instalação concluída!';
            }
        }

        // Esconder progresso de instalação
        function hideInstallProgress() {
            const banner = document.getElementById('pwa-install-banner');
            if (banner) {
                banner.style.display = 'none';
            }
        }

        // Abrir app instalado
        function openInstalledApp() {
            // Mostrar mensagem de sucesso
            showSuccessMessage();

            // Tentar abrir o app (funciona apenas se estiver instalado)
            setTimeout(() => {
                try {
                    // Recarregar a página para abrir como PWA
                    window.location.reload();
                } catch (error) {
                    console.log('App será aberto automaticamente');
                }
            }, 2000);
        }

        // Mostrar mensagem de sucesso
        function showSuccessMessage() {
            const successBanner = document.createElement('div');
            successBanner.className = 'pwa-success-banner';
            successBanner.innerHTML = `
                <div class="pwa-success-content">
                    <i class="fas fa-check-circle pwa-success-icon"></i>
                    <span class="pwa-success-text">App instalado com sucesso!</span>
                    <small class="pwa-success-subtitle">Abrindo aplicativo...</small>
                </div>
            `;

            document.body.appendChild(successBanner);

            // Remover após 3 segundos
            setTimeout(() => {
                if (successBanner.parentNode) {
                    successBanner.parentNode.removeChild(successBanner);
                }
            }, 3000);
        }

        // Rastrear instalação do PWA
        async function trackPWAInstall() {
            try {
                await fetch('/api/app_installs.php?action=track-install', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        event: 'app_installed',
                        timestamp: Date.now(),
                        user_agent: navigator.userAgent,
                        screen_width: screen.width,
                        screen_height: screen.height
                    })
                });
            } catch (error) {
                console.log('Error tracking install:', error);
            }
        }

        // Event listeners
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('PWA: Install prompt available');
            e.preventDefault();
            deferredPrompt = e;

            // Verificar se banner foi dispensado recentemente
            const dismissed = localStorage.getItem('pwa-banner-dismissed');
            if (!dismissed || Date.now() > parseInt(dismissed)) {
                // Mostrar banner após 3 segundos
                setTimeout(showPWABanner, 3000);
            }
        });

        window.addEventListener('appinstalled', () => {
            console.log('PWA: App installed');
            dismissPWABanner();
            deferredPrompt = null;

            // Mostrar banner de notificações após instalação
            setTimeout(() => {
                showNotificationBanner();
            }, 2000);
        });

        // Verificar e mostrar botão de notificações se necessário
        function checkNotificationStatus() {
            if ('Notification' in window) {
                const permission = Notification.permission;
                const isInstalled = isPWAInstalled();

                if (isInstalled && permission !== 'granted') {
                    // App já instalado mas notificações não ativas - mostrar banner de notificações
                    showNotificationBanner();
                } else if (permission === 'granted') {
                    // Notificações já ativas - esconder todos os banners
                    const pwaBanner = document.getElementById('pwa-install-banner');
                    const notificationBanner = document.getElementById('notification-banner');
                    if (pwaBanner) pwaBanner.style.display = 'none';
                    if (notificationBanner) notificationBanner.remove();
                }
            }
        }

        function showNotificationButton() {
            // Verificar se já existe banner de notificação
            if (document.getElementById('notification-banner')) {
                return;
            }

            const notificationBanner = document.createElement('div');
            notificationBanner.id = 'notification-banner';
            notificationBanner.className = 'pwa-install-banner-compact';
            notificationBanner.style.top = '70px'; // Abaixo do banner PWA se existir
            notificationBanner.innerHTML = `
                <div class="pwa-banner-content-compact">
                    <div class="pwa-banner-info">
                        <i class="fas fa-bell pwa-icon"></i>
                        <span class="pwa-text">Ativar notificações</span>
                    </div>
                    <div class="pwa-banner-actions">
                        <button onclick="requestNotifications()" class="pwa-install-btn-compact">
                            <i class="fas fa-bell"></i>
                            Ativar
                        </button>
                        <button onclick="dismissNotificationBanner()" class="pwa-close-btn-compact">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(notificationBanner);
        }

        function showNotificationBanner() {
            // Verificar se notificações já estão permitidas
            if ('Notification' in window && Notification.permission === 'granted') {
                return;
            }

            // Verificar se banner foi dispensado recentemente
            const dismissed = localStorage.getItem('notification-banner-dismissed');
            if (dismissed && Date.now() < parseInt(dismissed)) {
                return;
            }

            // Remover banner PWA se existir
            const pwaBanner = document.getElementById('pwa-install-banner');
            if (pwaBanner) {
                pwaBanner.style.display = 'none';
            }

            // Criar banner de notificações
            showNotificationButton();
        }

        async function requestNotifications() {
            if (typeof pwaManagerInstance !== 'undefined') {
                await pwaManagerInstance.requestNotificationPermission();

                // Se permissão foi concedida, configurar push notifications
                if (Notification.permission === 'granted') {
                    await pwaManagerInstance.setupPushNotifications();

                    // Esconder banner de notificações
                    dismissNotificationBanner();

                    // Mostrar mensagem de sucesso
                    showNotificationSuccessMessage();

                    console.log('✅ Notificações configuradas com sucesso!');
                }
            }
        }

        // Configurar notificações automaticamente se já permitidas
        function autoSetupNotifications() {
            if ('Notification' in window && Notification.permission === 'granted') {
                if (typeof pwaManagerInstance !== 'undefined') {
                    pwaManagerInstance.setupPushNotifications();
                    console.log('🔔 Auto-configurando notificações...');
                }
            }
        }

        function dismissNotificationBanner() {
            const banner = document.getElementById('notification-banner');
            if (banner) {
                banner.remove();
                // Salvar preferência para não mostrar novamente por 24 horas
                localStorage.setItem('notification-banner-dismissed', Date.now() + (24 * 60 * 60 * 1000));
            }
        }

        function showNotificationSuccessMessage() {
            const successBanner = document.createElement('div');
            successBanner.className = 'pwa-success-banner';
            successBanner.innerHTML = `
                <div class="pwa-success-content">
                    <i class="fas fa-bell pwa-success-icon"></i>
                    <span class="pwa-success-text">Notificações ativadas!</span>
                    <small class="pwa-success-subtitle">Você receberá alertas de promoções</small>
                </div>
            `;

            document.body.appendChild(successBanner);

            // Remover após 3 segundos
            setTimeout(() => {
                if (successBanner.parentNode) {
                    successBanner.parentNode.removeChild(successBanner);
                }
            }, 3000);
        }

        // Inicializar quando DOM estiver pronto
        document.addEventListener('DOMContentLoaded', function() {
            // Inicializar PWA Manager se existir
            if (typeof PWAManager !== 'undefined') {
                pwaManagerInstance = new PWAManager();

                // Auto-configurar notificações se já permitidas
                setTimeout(autoSetupNotifications, 2000);
            }

            // Verificar status de notificações após 3 segundos
            setTimeout(() => {
                const dismissed = localStorage.getItem('notification-banner-dismissed');
                if (!dismissed || Date.now() > parseInt(dismissed)) {
                    checkNotificationStatus();
                }
            }, 3000);


        });
    </script>
    <script>
        // Código das notificações
    const names = [
        "Lucas Silva completou a compra do Curso Completo: Monte Seu Próprio Site!",
        "Lucas Silva completou a compra do Curso Completo: Monte Seu Próprio Site!",
        "Ana Souza completou a compra do Curso Completo: Monte Seu Próprio Site!",
        "Carlos Pereira completou a compra do Curso Completo: Monte Seu Próprio Site!",
        "João Silva completou a compra do Script PHP - Modelo China para Casa de Apostas!",
        "Maria Oliveira completou a compra do Curso Completo: Facebook Ads Venda Certa!",
        "Paulo Santos completou a compra do Curso Completo: Monte Seu Próprio Site!",
        "Beatriz Costa completou a compra do Script PHP - Modelo China para Casa de Apostas!",
        "Felipe Almeida completou a compra do Script PHP - Painel Fiver Scan - Integrado com Pix!",
        "Roberta Lima completou a compra do Curso Completo: Facebook Ads Venda Certa!",
        "Eduardo Pereira completou a compra do Script PHP - Modelo p9bets para Casa de Apostas!",
        "Lucas Costa completou a compra do Curso Completo: Monte Seu Próprio Site!",
        "Amanda Rocha completou a compra do Script PHP - Modelo China para Casa de Apostas!",
        "Ricardo Torres completou a compra do Script PHP - Painel Fiver Scan - Integrado com Pix!",
        "Thiago Martins completou a compra do Curso Completo: Monte Seu Próprio Site!",
        "Jéssica Silva completou a compra do Curso Completo: Facebook Ads Venda Certa!",
        "Felipe Costa completou a compra do Script PHP - Modelo p9bets para Casa de Apostas!",
        "Juliana Mendes completou a compra do Script PHP - Modelo China para Casa de Apostas!",
        "Lucas Pereira completou a compra do Curso Completo: Facebook Ads Venda Certa!",
        "Daniele Souza completou a compra do Script PHP - Modelo China para Casa de Apostas!",
        "Mateus Lima completou a compra do Curso Completo: Monte Seu Próprio Site!",
        "Rafael Oliveira completou a compra do Script PHP - Painel Fiver Scan - Integrado com Pix!",
        "Tatiane Souza completou a compra do Curso Completo: Facebook Ads Venda Certa!",
        "Rodrigo Costa completou a compra do Script PHP - Modelo p9bets para Casa de Apostas!",
        "Bruna Costa completou a compra do Curso Completo: Monte Seu Próprio Site!",
        "Carla Ferreira completou a compra do Script PHP - Modelo China para Casa de Apostas!",
        "Vinícius Martins completou a compra do Script PHP - Painel Fiver Scan - Integrado com Pix!",
        "Marta Souza completou a compra do Curso Completo: Monte Seu Próprio Site!",
        "Gustavo Pereira completou a compra do Script PHP - Modelo p9bets para Casa de Apostas!",
        "Juliana Silva completou a compra do Curso Completo: Facebook Ads Venda Certa!",
        "Márcia Costa completou a compra do Script PHP - Modelo China para Casa de Apostas!",
        "Ana Souza completou a compra do Curso Completo: Monte Seu Próprio Site!",
        "Pedro Santos acabou de comprar o Curso Completo: Monte Seu Próprio Site!",
        "Maria Oliveira adquiriu o Curso Completo: Monte Seu Próprio Site!",
        "João Pereira comprou o Curso Completo: Monte Seu Próprio Site!"
    ];
    
    const usersOnline = [
        "Carlos Pereira está online agora!",
        "João Silva está online agora!",
        "Ana Souza está online agora!",
        "Felipe Almeida está online agora!",
        "Roberta Lima está online agora!",
        "Ricardo Torres está online agora!",
        "Lucas Costa está online agora!",
        "Amanda Rocha está online agora!",
        "Eduardo Pereira está online agora!",
        "Jéssica Silva está online agora!",
        "Paulo Santos está online agora!",
        "Beatriz Costa está online agora!",
        "Mateus Lima está online agora!",
        "Juliana Mendes está online agora!",
        "Lucas Pereira está online agora!",
        "Rafael Oliveira está online agora!",
        "Tatiane Souza está online agora!",
        "Rodrigo Costa está online agora!",
        "Bruna Costa está online agora!",
        "Carla Ferreira está online agora!",
        "Vinícius Martins está online agora!",
        "Marta Souza está online agora!",
        "Gustavo Pereira está online agora!",
        "Márcia Costa está online agora!",
        "Juliana Silva está online agora!",
        "Carlos Oliveira está online agora!",
        "Ricardo Costa está online agora!",
        "Renata Santos está online agora!",
        "Fábio Torres está online agora!",
        "Paula Almeida está online agora!",
        "Marcelo Lima está online agora!"
    ];

        let index = 0;
        let onlineIndex = 0;
        let toggle = true;

        const notification = document.createElement("div");
        notification.className = "notification";
        document.body.appendChild(notification);

        function showNotification() {
            notification.textContent = toggle ? names[index] : usersOnline[onlineIndex];
            notification.classList.add(toggle ? "purchase" : "online");
            notification.classList.add("active");

            setTimeout(() => {
                notification.classList.remove("active");
                notification.classList.remove(toggle ? "purchase" : "online");

                if (toggle) {
                    index = (index + 1) % names.length;
                } else {
                    onlineIndex = (onlineIndex + 1) % usersOnline.length;
                }
                toggle = !toggle;
            }, 5000);
        }

        // Mostrar primeira notificação após 3 segundos
        setTimeout(() => {
            showNotification();
            // Continuar mostrando a cada 15 segundos
            setInterval(showNotification, 15000);
        }, 3000);

        // Código do banner de cookies
        const cookieBanner = document.getElementById('cookieBanner');
        const acceptButton = document.getElementById('accept-cookies');
        const declineButton = document.getElementById('decline-cookies');

        // Verifica se o usuário já fez uma escolha
        if (!localStorage.getItem('cookieChoice')) {
            cookieBanner.style.display = 'flex';
        }

        acceptButton.addEventListener('click', () => {
            localStorage.setItem('cookieChoice', 'accepted');
            cookieBanner.style.display = 'none';
        });

        declineButton.addEventListener('click', () => {
            localStorage.setItem('cookieChoice', 'declined');
            cookieBanner.style.display = 'none';
        });

        function openPrivacyPolicy() {
            const privacyModal = new bootstrap.Modal(document.getElementById('privacyPolicyModal'));
            privacyModal.show();
            return false;
        }


        // // Bloquear o menu de contexto (botão direito)
        // document.addEventListener('contextmenu', function (e) {
        //     e.preventDefault();
        // }, false);
        
        // // Bloquear atalhos de teclado
        // document.addEventListener('keydown', function (e) {
        //     // F12 (Ferramentas de desenvolvedor)
        //     if (e.key === 'F12' ||
        //         (e.ctrlKey && e.shiftKey && (e.key === 'I' || e.key === 'J')) ||
        //         (e.ctrlKey && e.key === 'U') || // Ctrl + U (visualizar código-fonte)
        //         (e.ctrlKey && e.key === 'T') || // Ctrl + T (Nova aba)
        //         (e.ctrlKey && e.key === 'R') || // Ctrl + R (Recarregar página)
        //         (e.shiftKey && e.key === 'Delete') || // Shift + Delete
        //         (e.ctrlKey && e.key === 'W') || // Ctrl + W (Fechar aba)
        //         (e.ctrlKey && e.key === 'N') || // Ctrl + N (Nova janela)
        //         (e.ctrlKey && e.key === 'P') || // Ctrl + P (Imprimir)
        //         (e.ctrlKey && e.key === 'S') || // Ctrl + S (Salvar página)
        //         (e.ctrlKey && e.key === 'U')    // Ctrl + U (view-source)
        //     ) {
        //         e.preventDefault();
        //     }
        // }, false);
        
        // Impedir a visualização do código-fonte (view-source) usando Ctrl + U
        window.addEventListener('keydown', function (e) {
            // Bloquear Ctrl + U (visualizar código-fonte)
            if (e.ctrlKey && e.key === 'U') {
                e.preventDefault();
            }
        
            // Impedir 'view-source' (Ctrl + S) no navegador
            if (e.ctrlKey && e.key === 'S') { // Ctrl + S (salvar página)
                e.preventDefault();
            }
        }, false);
        
        // Impedir a tecla F12 (Ferramentas de desenvolvedor)
        window.addEventListener('keydown', function (e) {
            if (e.key === 'F12') {
                e.preventDefault();
            }
        }, false);
        
        // Impedir a navegação para outras páginas ou recarregar a página
        window.addEventListener('beforeunload', function (e) {
            e.preventDefault();
            e.returnValue = ''; // Chrome exige essa linha para bloquear saída
        });




        
        // Código para o modal de imagem
        document.addEventListener('DOMContentLoaded', function() {
            // Adiciona evento de clique em cada imagem
            document.querySelectorAll('.card-image').forEach(img => {
                img.addEventListener('click', function() {
                    const modal = new bootstrap.Modal(document.getElementById('imageModal'));
                    document.getElementById('modalImage').src = this.src;
                    document.getElementById('modalTitle').textContent = this.alt;
                    modal.show();
                });
            });

            document.getElementById('openChat').addEventListener('click', () => {
                window.open('http://localhost/chat.php', '_blank', 'width=400,height=600,location=no,menubar=no,toolbar=no,status=no');
            });
        });
    </script>
</body>
</html>
