<?php
/**
 * Página SEO: Scripts PHP - KESUNG SITE
 * Otimizada para ranquear em "script php", "sistema php", "código php pronto"
 */

// Conexão com banco usando .env
require_once __DIR__ . '/includes/env_loader.php';

try {
    $pdo = new PDO(
        "mysql:host=" . EnvLoader::get('DB_HOST', 'localhost') . ";dbname=" . EnvLoader::get('DB_NAME', 'u276254152_banco_loja') . ";charset=utf8mb4",
        EnvLoader::get('DB_USER', 'root'),
        EnvLoader::get('DB_PASS', ''),
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    // Buscar scripts PHP
    $stmt = $pdo->query("
        SELECT * FROM products 
        WHERE status = 'active' 
        AND (name LIKE '%script%' OR name LIKE '%php%' OR name LIKE '%sistema%')
        ORDER BY created_at DESC
    ");
    $scripts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $scripts = [];
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- SEO Meta Tags -->
    <title>Scripts PHP Profissionais 2024 - Sistemas Completos e Códigos Prontos | KESUNG SITE</title>
    <meta name="description" content="Scripts PHP profissionais para casa de apostas, sistemas de pagamento PIX, painéis administrativos e muito mais. Códigos prontos para usar e personalizar.">
    <meta name="keywords" content="script php, sistema php, código php pronto, script casa apostas, sistema pix php, painel administrativo php, script php completo, desenvolvimento php, sistema web php">
    
    <!-- Open Graph -->
    <meta property="og:title" content="Scripts PHP Profissionais 2024 - KESUNG SITE">
    <meta property="og:description" content="Scripts PHP profissionais para casa de apostas, sistemas de pagamento PIX, painéis administrativos e muito mais.">
    <meta property="og:image" content="https://kesungsite.com/assets/images/scripts-php-og.jpg">
    <meta property="og:url" content="https://kesungsite.com/scripts-php">
    
    <!-- Schema.org -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Product",
        "name": "Scripts PHP Profissionais",
        "description": "Scripts PHP profissionais para diversos tipos de sistemas web",
        "brand": {
            "@type": "Organization",
            "name": "KESUNG SITE"
        },
        "category": "Software",
        "offers": {
            "@type": "AggregateOffer",
            "priceCurrency": "BRL",
            "lowPrice": "297",
            "highPrice": "997"
        }
    }
    </script>
    
    <link rel="canonical" href="https://kesungsite.com/scripts-php">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <header class="bg-success text-white py-4">
        <div class="container">
            <nav class="d-flex justify-content-between align-items-center">
                <a href="/" class="text-white text-decoration-none">
                    <h1 class="h3 mb-0">KESUNG SITE</h1>
                </a>
                <div>
                    <a href="/" class="text-white me-3">Início</a>
                    <a href="/cursos-programacao" class="text-white me-3">Cursos</a>
                    <a href="/casa-apostas" class="text-white me-3">Casa de Apostas</a>
                    <a href="/marketing-digital" class="text-white">Marketing Digital</a>
                </div>
            </nav>
        </div>
    </header>

    <main class="container my-5">
        <!-- Hero Section -->
        <section class="text-center mb-5">
            <h1 class="display-4 fw-bold text-success mb-3">
                Scripts PHP Profissionais 2024
            </h1>
            <p class="lead text-muted mb-4">
                Scripts PHP completos e otimizados para casa de apostas, sistemas de pagamento PIX, 
                painéis administrativos e muito mais. Códigos prontos para usar e personalizar.
            </p>
            <div class="row text-center">
                <div class="col-md-3">
                    <i class="fas fa-code fa-3x text-success mb-2"></i>
                    <h5>Código Limpo</h5>
                    <p class="small">Scripts organizados e comentados</p>
                </div>
                <div class="col-md-3">
                    <i class="fas fa-shield-alt fa-3x text-primary mb-2"></i>
                    <h5>Segurança</h5>
                    <p class="small">Proteção contra SQL Injection e XSS</p>
                </div>
                <div class="col-md-3">
                    <i class="fas fa-mobile-alt fa-3x text-info mb-2"></i>
                    <h5>Responsivo</h5>
                    <p class="small">Funciona em todos os dispositivos</p>
                </div>
                <div class="col-md-3">
                    <i class="fas fa-tools fa-3x text-warning mb-2"></i>
                    <h5>Personalizável</h5>
                    <p class="small">Fácil de customizar e expandir</p>
                </div>
            </div>
        </section>

        <!-- Scripts Disponíveis -->
        <section class="mb-5">
            <h2 class="h3 mb-4">Nossos Scripts PHP</h2>
            <div class="row">
                <?php foreach ($scripts as $script): ?>
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <?php if (!empty($script['image'])): ?>
                        <img src="uploads/products/<?php echo htmlspecialchars($script['image']); ?>" 
                             class="card-img-top" alt="<?php echo htmlspecialchars($script['name']); ?>"
                             style="height: 200px; object-fit: cover;">
                        <?php endif; ?>
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title"><?php echo htmlspecialchars($script['name']); ?></h5>
                            <p class="card-text flex-grow-1"><?php echo htmlspecialchars($script['description']); ?></p>
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span class="h4 text-success mb-0">
                                        R$ <?php echo number_format($script['price'], 2, ',', '.'); ?>
                                    </span>
                                    <small class="text-muted">
                                        <i class="fas fa-download text-success"></i> Pronto para usar
                                    </small>
                                </div>
                                <button class="btn btn-success w-100" onclick="buyProduct(<?php echo $script['id']; ?>)">
                                    <i class="fas fa-shopping-cart me-2"></i>Comprar Script
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </section>

        <!-- SEO Content -->
        <section class="mb-5">
            <div class="row">
                <div class="col-lg-8">
                    <h2>Scripts PHP Profissionais para Seu Negócio</h2>
                    <p>Nossos scripts PHP são desenvolvidos seguindo as melhores práticas de programação e segurança. Cada script inclui:</p>
                    
                    <h3>Scripts para Casa de Apostas</h3>
                    <p>Scripts completos para casa de apostas esportivas com integração PIX, painel administrativo, sistema de odds dinâmicas e muito mais. Ideais para quem quer entrar no mercado de apostas online.</p>
                    
                    <h3>Sistemas de Pagamento PIX</h3>
                    <p>Integração completa com APIs de pagamento PIX, incluindo geração de QR codes, webhook de confirmação e painel de controle financeiro. Perfeito para e-commerce e sistemas de vendas.</p>
                    
                    <h3>Painéis Administrativos</h3>
                    <p>Painéis completos com dashboard, relatórios, gestão de usuários, sistema de permissões e muito mais. Economize meses de desenvolvimento com nossos scripts prontos.</p>
                    
                    <h3>Sistemas Personalizados</h3>
                    <p>Scripts para diversos nichos: sistemas de afiliados, plataformas de cursos online, marketplaces, sistemas de delivery e muito mais.</p>
                </div>
                <div class="col-lg-4">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h5>O que está incluso:</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Código fonte completo</li>
                                <li><i class="fas fa-check text-success me-2"></i>Banco de dados SQL</li>
                                <li><i class="fas fa-check text-success me-2"></i>Manual de instalação</li>
                                <li><i class="fas fa-check text-success me-2"></i>Documentação técnica</li>
                                <li><i class="fas fa-check text-success me-2"></i>Suporte por 30 dias</li>
                                <li><i class="fas fa-check text-success me-2"></i>Atualizações gratuitas</li>
                                <li><i class="fas fa-check text-success me-2"></i>Design responsivo</li>
                                <li><i class="fas fa-check text-success me-2"></i>Código comentado</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Tecnologias -->
        <section class="mb-5">
            <h2>Tecnologias Utilizadas</h2>
            <div class="row text-center">
                <div class="col-md-2 col-4 mb-3">
                    <i class="fab fa-php fa-3x text-primary mb-2"></i>
                    <h6>PHP 8+</h6>
                </div>
                <div class="col-md-2 col-4 mb-3">
                    <i class="fas fa-database fa-3x text-info mb-2"></i>
                    <h6>MySQL</h6>
                </div>
                <div class="col-md-2 col-4 mb-3">
                    <i class="fab fa-js fa-3x text-warning mb-2"></i>
                    <h6>JavaScript</h6>
                </div>
                <div class="col-md-2 col-4 mb-3">
                    <i class="fab fa-bootstrap fa-3x text-purple mb-2"></i>
                    <h6>Bootstrap</h6>
                </div>
                <div class="col-md-2 col-4 mb-3">
                    <i class="fab fa-html5 fa-3x text-danger mb-2"></i>
                    <h6>HTML5</h6>
                </div>
                <div class="col-md-2 col-4 mb-3">
                    <i class="fab fa-css3-alt fa-3x text-primary mb-2"></i>
                    <h6>CSS3</h6>
                </div>
            </div>
        </section>

        <!-- FAQ -->
        <section class="mb-5">
            <h2>Perguntas Frequentes</h2>
            <div class="accordion" id="faqAccordion">
                <div class="accordion-item">
                    <h3 class="accordion-header">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                            Os scripts funcionam em qualquer hospedagem?
                        </button>
                    </h3>
                    <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            Sim! Nossos scripts são compatíveis com qualquer hospedagem que tenha PHP 7.4+ e MySQL. Incluímos manual de instalação detalhado.
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <h3 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                            Posso personalizar o design dos scripts?
                        </button>
                    </h3>
                    <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            Claro! Todos os scripts incluem código fonte completo e são totalmente personalizáveis. Você pode alterar cores, layout, funcionalidades e muito mais.
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <h3 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                            Há suporte técnico incluído?
                        </button>
                    </h3>
                    <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            Sim! Oferecemos 30 dias de suporte técnico gratuito para instalação e configuração básica. Suporte estendido disponível separadamente.
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="bg-dark text-white py-4">
        <div class="container text-center">
            <p>&copy; 2024 KESUNG SITE. Todos os direitos reservados.</p>
            <div>
                <a href="/termos-uso" class="text-white me-3">Termos de Uso</a>
                <a href="/politica-privacidade" class="text-white">Política de Privacidade</a>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/simple_pix.js"></script>
</body>
</html>
