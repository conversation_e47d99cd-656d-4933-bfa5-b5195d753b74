<?php
/**
 * Configurações do Sistema - KESUNG SITE
 * Usando variáveis de ambiente (.env)
 */

// Carregar variáveis de ambiente
require_once __DIR__ . '/env_loader.php';

// Configurações do banco de dados via .env
define('DB_HOST', EnvLoader::get('DB_HOST', 'localhost'));
define('DB_NAME', EnvLoader::get('DB_NAME', 'u276254152_banco_loja'));
define('DB_USER', EnvLoader::get('DB_USER', 'root'));
define('DB_PASS', EnvLoader::get('DB_PASS', ''));

try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
} catch (PDOException $e) {
    die("Erro na conexão com o banco de dados: " . $e->getMessage());
}

// Iniciar sessão se ainda não foi iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Configurações de upload
if (!defined('UPLOAD_DIR_ADMIN')) {
    define('UPLOAD_DIR_ADMIN', __DIR__ . '/../admin/uploads/products/');
}
if (!defined('UPLOAD_DIR_FRONT')) {
    define('UPLOAD_DIR_FRONT', __DIR__ . '/../imagens/products/');
}

// Criar diretórios se não existirem
if (!is_dir(UPLOAD_DIR_ADMIN)) {
    mkdir(UPLOAD_DIR_ADMIN, 0777, true);
}
if (!is_dir(UPLOAD_DIR_FRONT)) {
    mkdir(UPLOAD_DIR_FRONT, 0777, true);
}

// Funções úteis
function formatPrice($price) {
    return number_format($price, 2, ',', '.');
}

function sanitizeString($str) {
    return htmlspecialchars(trim($str), ENT_QUOTES, 'UTF-8');
}
