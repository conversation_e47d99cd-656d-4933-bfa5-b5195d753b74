<div class="bg-white" id="sidebar-wrapper">
    <style>
    .sidebar-section {
        margin-bottom: 20px;
    }

    .sidebar-section-title {
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        color: #6c757d;
        padding: 8px 15px;
        margin-bottom: 5px;
        border-bottom: 1px solid #e9ecef;
    }

    .list-group-item {
        border: none;
        padding: 10px 20px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .list-group-item:hover {
        background-color: #f8f9fa;
        transform: translateX(5px);
    }

    .list-group-item.active {
        background-color: #007bff;
        color: white;
        border-radius: 0 25px 25px 0;
        margin-right: 10px;
    }

    .list-group-item i {
        width: 20px;
        margin-right: 10px;
        text-align: center;
    }
    </style>

    <div class="sidebar-heading text-center py-4">
        <i class="fas fa-user-shield fa-2x text-primary"></i>
        <div class="mt-2 fw-bold">Painel Admin</div>
    </div>

    <div class="px-3">
        <!-- PRINCIPAL -->
        <div class="sidebar-section">
            <div class="sidebar-section-title">
                <i class="fas fa-home me-2"></i>Principal
            </div>
            <div class="list-group list-group-flush">
                <a href="dashboard.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </div>
        </div>

        <!-- VENDAS -->
        <div class="sidebar-section">
            <div class="sidebar-section-title">
                <i class="fas fa-shopping-cart me-2"></i>Vendas
            </div>
            <div class="list-group list-group-flush">
                <a href="sales.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'sales.php' ? 'active' : ''; ?>">
                    <i class="fas fa-shopping-cart"></i>Vendas
                </a>
                <a href="products.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'products.php' ? 'active' : ''; ?>">
                    <i class="fas fa-box"></i>Produtos
                </a>
                <a href="customers.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'customers.php' ? 'active' : ''; ?>">
                    <i class="fas fa-users"></i>Clientes
                </a>
            </div>
        </div>

        <!-- RELATÓRIOS -->
        <div class="sidebar-section">
            <div class="sidebar-section-title">
                <i class="fas fa-chart-bar me-2"></i>Relatórios
            </div>
            <div class="list-group list-group-flush">
                <a href="reports.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'reports.php' ? 'active' : ''; ?>">
                    <i class="fas fa-chart-line"></i>Relatórios
                </a>
            </div>
        </div>

        <!-- CONFIGURAÇÕES -->
        <div class="sidebar-section">
            <div class="sidebar-section-title">
                <i class="fas fa-cog me-2"></i>Configurações
            </div>
            <div class="list-group list-group-flush">
                <a href="settings.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'settings.php' ? 'active' : ''; ?>">
                    <i class="fas fa-cog"></i>Configurações
                </a>
                <a href="payment_settings.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'payment_settings.php' ? 'active' : ''; ?>">
                    <i class="fas fa-credit-card"></i>Config. Pagamento
                </a>
                <a href="api_settings.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'api_settings.php' ? 'active' : ''; ?>">
                    <i class="fas fa-key"></i>Config. API
                </a>
            </div>
        </div>

        <!-- COMUNICAÇÃO -->
        <div class="sidebar-section">
            <div class="sidebar-section-title">
                <i class="fas fa-envelope me-2"></i>Comunicação
            </div>
            <div class="list-group list-group-flush">
                <a href="chat.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'chat.php' ? 'active' : ''; ?>">
                    <i class="fas fa-comments"></i>Atendimento
                </a>
                <a href="email_settings.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'email_settings.php' ? 'active' : ''; ?>">
                    <i class="fas fa-envelope-open-text"></i>Config. Email
                </a>
                <a href="email_templates.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'email_templates.php' ? 'active' : ''; ?>">
                    <i class="fas fa-envelope"></i>Templates Email
                </a>
                <a href="email_logs.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'email_logs.php' ? 'active' : ''; ?>">
                    <i class="fas fa-history"></i>Logs Email
                </a>
            </div>
        </div>

        <!-- SEGURANÇA -->
        <div class="sidebar-section">
            <div class="sidebar-section-title">
                <i class="fas fa-shield-alt me-2"></i>Segurança
            </div>
            <div class="list-group list-group-flush">
                <a href="visitors.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'visitors.php' ? 'active' : ''; ?>">
                    <i class="fas fa-eye"></i>Logs Visitantes
                </a>
                <a href="app_management.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'app_management.php' ? 'active' : ''; ?>">
                    <i class="fas fa-mobile-alt"></i>Gerenciar App
                </a>
            </div>
        </div>

        <!-- SAIR -->
        <div class="sidebar-section">
            <div class="list-group list-group-flush">
                <a href="logout.php" class="list-group-item list-group-item-action text-danger">
                    <i class="fas fa-sign-out-alt"></i>Sair
                </a>
            </div>
        </div>
    </div>
</div>
