<?php
require_once 'includes/auth_check.php';

$db = Database::getInstance();
$pdo = $db->getConnection();

// Verificar se a tabela existe, se não, criar
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'payment_credentials'");
    if ($stmt->rowCount() == 0) {
        $createTable = "
            CREATE TABLE payment_credentials (
                id INT AUTO_INCREMENT PRIMARY KEY,
                api_key VARCHAR(500),
                wallet_id VARCHAR(255),
                environment ENUM('sandbox', 'production') DEFAULT 'production',
                public_key VARCHAR(255),
                access_token VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ";
        $pdo->exec($createTable);
    }
} catch (Exception $e) {
    // Tabela já existe ou erro na criação
}

// Processar o formulário quando enviado
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $api_key = $_POST['api_key'] ?? '';
    $wallet_id = $_POST['wallet_id'] ?? '';
    $environment = $_POST['environment'] ?? 'production';
    $public_key = $_POST['public_key'] ?? '';
    $access_token = $_POST['access_token'] ?? '';

    // Verifica se já existe registro
    try {
        $stmt = $pdo->query("SELECT id FROM payment_credentials LIMIT 1");
        $exists = $stmt->fetch();
    } catch (Exception $e) {
        $exists = false;
    }

    try {
        if ($exists) {
            // Atualiza
            $stmt = $pdo->prepare("UPDATE payment_credentials SET api_key = ?, wallet_id = ?, environment = ?, public_key = ?, access_token = ? WHERE id = ?");
            $stmt->execute([$api_key, $wallet_id, $environment, $public_key, $access_token, $exists['id']]);
        } else {
            // Insere
            $stmt = $pdo->prepare("INSERT INTO payment_credentials (api_key, wallet_id, environment, public_key, access_token) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$api_key, $wallet_id, $environment, $public_key, $access_token]);
        }
        echo "<script>
            Swal.fire({
                title: 'Configurações salvas com sucesso!',
                icon: 'success',
                confirmButtonColor: '#0d6efd'
            });
        </script>";
    } catch (PDOException $e) {
        echo "<script>
            Swal.fire({
                title: 'Erro ao salvar as configurações',
                icon: 'error',
                confirmButtonColor: '#0d6efd'
            });
        </script>";
    }
}

// Buscar credenciais atuais
try {
    $stmt = $pdo->query("SELECT * FROM payment_credentials LIMIT 1");
    $credentials = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $credentials = [];
}

require_once 'includes/header.php';
?>

<div class="container-fluid px-4">
    <h2 class="fs-2 mb-4">Configurações de Pagamento</h2>

    <div class="card">
        <div class="card-body">
            <h5 class="card-title">
                <i class="fas fa-credit-card"></i>
                Configurações Asaas PIX
            </h5>

            <form method="POST" action="">
                        <div class="form-group mb-3">
                            <label for="api_key">API Key Asaas</label>
                            <input type="text"
                                   class="form-control"
                                   id="api_key"
                                   name="api_key"
                                   value="<?php echo htmlspecialchars($credentials['api_key'] ?? ''); ?>"
                                   placeholder="aact_prod_000..."
                                   required>
                            <small class="form-text text-muted">
                                Chave de API do Asaas para autenticação
                            </small>
                        </div>

                        <div class="form-group mb-3">
                            <label for="wallet_id">Wallet ID</label>
                            <input type="text"
                                   class="form-control"
                                   id="wallet_id"
                                   name="wallet_id"
                                   value="<?php echo htmlspecialchars($credentials['wallet_id'] ?? ''); ?>"
                                   placeholder="47f32de3-8b86-4efb-b51c-928e4fdd684f">
                            <small class="form-text text-muted">
                                ID da carteira Asaas (opcional)
                            </small>
                        </div>

                        <div class="form-group mb-3">
                            <label for="environment">Ambiente</label>
                            <select class="form-control" id="environment" name="environment">
                                <option value="production" <?php echo ($credentials['environment'] ?? 'production') === 'production' ? 'selected' : ''; ?>>
                                    Produção
                                </option>
                                <option value="sandbox" <?php echo ($credentials['environment'] ?? '') === 'sandbox' ? 'selected' : ''; ?>>
                                    Sandbox (Teste)
                                </option>
                            </select>
                            <small class="form-text text-muted">
                                Ambiente de operação da API
                            </small>
                        </div>

                        <hr>
                        <h6>Configurações Legadas (Opcional)</h6>

                        <div class="form-group mb-3">
                            <label for="public_key">Chave Pública (Public Key)</label>
                            <input type="text"
                                   class="form-control"
                                   id="public_key"
                                   name="public_key"
                                   value="<?php echo htmlspecialchars($credentials['public_key'] ?? ''); ?>">
                            <small class="form-text text-muted">
                                Chave pública (se necessário)
                            </small>
                        </div>

                        <div class="form-group mb-3">
                            <label for="access_token">Token de Acesso (Access Token)</label>
                            <input type="password"
                                   class="form-control"
                                   id="access_token"
                                   name="access_token"
                                   value="<?php echo htmlspecialchars($credentials['access_token'] ?? ''); ?>">
                            <small class="form-text text-muted">
                                Token de acesso adicional (se necessário)
                            </small>
                        </div>
                        
                        <div class="form-group">
                            <label>Última Atualização</label>
                            <p class="form-control-static">
                                <?php 
                                if (isset($credentials['updated_at'])) {
                                    echo date('d/m/Y H:i:s', strtotime($credentials['updated_at']));
                                } else {
                                    echo 'Nunca atualizado';
                                }
                                ?>
                            </p>
                        </div>
                        
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    Salvar Configurações
                </button>
            </form>

            <hr class="my-4">

            <div class="alert alert-info">
                <h5>
                    <i class="fas fa-info-circle"></i>
                    Como configurar
                </h5>
                <ol class="mb-0">
                    <li>Acesse sua conta no provedor de pagamento (Asaas)</li>
                    <li>Vá até as configurações de API/Integração</li>
                    <li>Copie a chave pública e o token de acesso</li>
                    <li>Cole as informações nos campos acima</li>
                    <li>Clique em "Salvar Configurações"</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<script>
// Remover o preventDefault para permitir o envio normal do formulário
document.querySelector('form').addEventListener('submit', function(e) {
    // Não prevenir o envio, apenas mostrar loading
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Salvando...';
    submitBtn.disabled = true;
});
</script>

<?php require_once 'includes/footer.php'; ?>

