<?php
require_once 'session.php';
require_once 'database/connection.php';

// Inicializa variáveis de filtro
$search = $_GET['search'] ?? '';
$start_date = $_GET['start_date'] ?? '';
$end_date = $_GET['end_date'] ?? '';
$status = $_GET['status'] ?? '';

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // Construir a query base
    $query = "SELECT s.*, c.name as customer_name
              FROM sales s
              LEFT JOIN customers c ON s.customer_email = c.email
              WHERE 1=1";
    $params = [];
    
    // Adicionar filtros
    if ($search) {
        $query .= " AND (s.customer_name LIKE ? OR s.customer_email LIKE ? OR s.id LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    if ($start_date) {
        $query .= " AND DATE(s.created_at) >= ?";
        $params[] = $start_date;
    }
    if ($end_date) {
        $query .= " AND DATE(s.created_at) <= ?";
        $params[] = $end_date;
    }
    if ($status) {
        $query .= " AND s.payment_status = ?";
        $params[] = $status;
    }

    $query .= " ORDER BY s.created_at DESC";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $sales = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error = 'Erro ao carregar vendas: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vendas - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/standardized.css">
</head>
<body>
    <div class="d-flex" id="wrapper">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>
        
        <!-- Page Content -->
        <div id="page-content-wrapper">
            <!-- Navbar -->
            <?php include 'includes/navbar.php'; ?>
            
            <div class="container-fluid px-4">
                <div class="row">
                    <div class="col-12">
                        <!-- Header -->
                        <div class="page-header mb-4">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h1 class="page-title">
                                        <i class="fas fa-shopping-cart me-3"></i>
                                        Vendas
                                    </h1>
                                    <p class="page-subtitle mb-0">Gerencie todas as vendas do sistema</p>
                                </div>
                                <div class="page-actions">
                                    <a href="new_sale.php" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>
                                        Nova Venda
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Filtros -->
                        <div class="card dashboard-card mb-4">
                            <div class="card-body">
                                <h5 class="card-title mb-3">
                                    <i class="fas fa-filter me-2"></i>
                                    Filtros de Busca
                                </h5>
                                <form method="GET" class="row g-3">
                                    <div class="col-md-3">
                                        <label class="form-label">Buscar</label>
                                        <input type="text"
                                               class="form-control"
                                               name="search"
                                               value="<?php echo htmlspecialchars($search); ?>"
                                               placeholder="Cliente ou ID da venda">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">Data Inicial</label>
                                        <input type="date"
                                               class="form-control"
                                               name="start_date"
                                               value="<?php echo $start_date; ?>">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">Data Final</label>
                                        <input type="date"
                                               class="form-control"
                                               name="end_date"
                                               value="<?php echo $end_date; ?>">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">Status</label>
                                        <select class="form-select" name="status">
                                            <option value="">Todos os status</option>
                                            <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>Pendente</option>
                                            <option value="completed" <?php echo $status === 'completed' ? 'selected' : ''; ?>>Concluído</option>
                                            <option value="cancelled" <?php echo $status === 'cancelled' ? 'selected' : ''; ?>>Cancelado</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">&nbsp;</label>
                                        <div class="d-flex gap-2">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-search me-1"></i>
                                                Filtrar
                                            </button>
                                            <a href="sales.php" class="btn btn-outline-secondary">
                                                <i class="fas fa-undo me-1"></i>
                                                Limpar
                                            </a>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <?php if (isset($error)): ?>
                        <div class="alert alert-danger">
                            <?php echo $error; ?>
                        </div>
                        <?php else: ?>
                        
                        <!-- Tabela de Vendas -->
                        <div class="card dashboard-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-list me-2"></i>
                                        Lista de Vendas
                                    </h5>
                                    <span class="badge bg-primary"><?php echo count($sales); ?> vendas</span>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-dashboard">
                                        <thead>
                                            <tr>
                                                <th>#</th>
                                                <th>Cliente</th>
                                                <th>Data</th>
                                                <th>Valor</th>
                                                <th>Método</th>
                                                <th>Status</th>
                                                <th>Ações</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($sales as $sale): ?>
                                            <tr>
                                                <td>
                                                    <span class="fw-bold text-primary">
                                                        #<?php echo str_pad($sale['id'], 5, '0', STR_PAD_LEFT); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <i class="fas fa-user-circle me-2 text-muted"></i>
                                                        <?php echo htmlspecialchars($sale['customer_name']); ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <small class="text-muted">
                                                        <?php echo date('d/m/Y H:i', strtotime($sale['created_at'])); ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <span class="fw-bold text-success">
                                                        R$ <?php echo number_format($sale['total_amount'], 2, ',', '.'); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-light text-dark">
                                                        <?php
                                                            $payment_method = $sale['payment_method'] ?? 'N/A';
                                                            echo strtoupper($payment_method);
                                                        ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="status-badge status-<?php echo $sale['payment_status']; ?>">
                                                        <?php
                                                        $status_labels = [
                                                            'pending' => 'Pendente',
                                                            'approved' => 'Aprovado',
                                                            'completed' => 'Concluído',
                                                            'cancelled' => 'Cancelado',
                                                            'refunded' => 'Reembolsado'
                                                        ];
                                                        echo $status_labels[$sale['payment_status']] ?? $sale['payment_status'];
                                                        ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="view_sale.php?id=<?php echo $sale['id']; ?>"
                                                           class="btn btn-sm btn-outline-info"
                                                           title="Ver detalhes">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <?php if ($sale['payment_status'] === 'pending'): ?>
                                                        <button type="button"
                                                                class="btn btn-sm btn-outline-success"
                                                                onclick="updateStatus(<?php echo $sale['id']; ?>, 'completed')"
                                                                title="Marcar como concluído">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                        <button type="button"
                                                                class="btn btn-sm btn-outline-danger"
                                                                onclick="updateStatus(<?php echo $sale['id']; ?>, 'cancelled')"
                                                                title="Cancelar venda">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>

                                            <?php if (empty($sales)): ?>
                                            <tr>
                                                <td colspan="7" class="text-center py-5">
                                                    <div class="empty-state">
                                                        <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                                        <h5 class="text-muted">Nenhuma venda encontrada</h5>
                                                        <p class="text-muted mb-0">Não há vendas que correspondam aos filtros aplicados.</p>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/scripts.js"></script>
    <script>
    function updateStatus(id, status) {
        if (confirm('Tem certeza que deseja alterar o status desta venda?')) {
            fetch('api/update_sale_status.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    id: id,
                    status: status
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Erro ao atualizar status: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Erro ao atualizar status');
            });
        }
    }
    </script>
</body>
</html>
