<?php
/**
 * Configurações Seguras Centralizadas
 * Sistema KESUNG - Proteção de Credenciais Sensíveis
 */

class SecureConfig {
    private static $config = null;
    private static $encryptionKey = null;
    
    /**
     * Obter configuração de forma segura
     */
    public static function get($key) {
        if (self::$config === null) {
            self::loadConfig();
        }
        return self::$config[$key] ?? null;
    }
    
    /**
     * Carregar configurações de forma segura
     */
    private static function loadConfig() {
        // Carregar arquivo .env se existir
        self::loadEnvFile();

        // Configurações com valores padrão seguros
        self::$config = [
            // Banco de dados
            'db_host' => self::getEnvVar('DB_HOST', 'localhost'),
            'db_name' => self::getEnvVar('DB_NAME', 'u276254152_banco_loja'),
            'db_user' => self::getEnvVar('DB_USER', 'root'),
            'db_pass' => self::getEnvVar('DB_PASS', ''),
            'db_charset' => 'utf8mb4',
            
            // PIX/Pagamentos
            'pix_api_url' => 'https://www.asaas.com/api/v3',
            'pix_api_key' => self::getEnvVar('PIX_API_KEY', ''),
            'pix_timeout' => 30,
            
            // Webhooks
            'webhook_secret' => self::getEnvVar('WEBHOOK_SECRET', self::generateWebhookSecret()),
            'webhook_token' => self::getEnvVar('WEBHOOK_TOKEN', self::generateWebhookToken()),
            
            // Segurança
            'session_timeout' => 3600, // 1 hora
            'max_login_attempts' => 5,
            'login_lockout_time' => 900, // 15 minutos
            'csrf_token_lifetime' => 1800, // 30 minutos
            
            // Upload
            'max_upload_size' => 10 * 1024 * 1024, // 10MB
            'allowed_image_types' => ['jpg', 'jpeg', 'png', 'gif'],
            'allowed_document_types' => ['pdf', 'doc', 'docx'],
            
            // Email
            'email_from' => '<EMAIL>',
            'email_reply_to' => '<EMAIL>',
            
            // Logs
            'log_level' => 'INFO',
            'log_max_size' => 10 * 1024 * 1024, // 10MB
            'log_retention_days' => 30
        ];
    }
    
    /**
     * Carregar arquivo .env
     */
    private static function loadEnvFile() {
        $envFile = __DIR__ . '/../.env';
        if (file_exists($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos($line, '#') === 0) continue; // Comentário
                if (strpos($line, '=') !== false) {
                    list($key, $value) = explode('=', $line, 2);
                    $key = trim($key);
                    $value = trim($value);
                    if (!array_key_exists($key, $_ENV)) {
                        $_ENV[$key] = $value;
                    }
                }
            }
        }
    }

    /**
     * Obter variável de ambiente com fallback
     */
    private static function getEnvVar($key, $default = null) {
        $value = $_ENV[$key] ?? getenv($key) ?? $default;

        // Se for uma chave sensível e estiver vazia, gerar uma
        if (empty($value) && in_array($key, ['WEBHOOK_SECRET', 'WEBHOOK_TOKEN', 'ENCRYPTION_KEY'])) {
            $value = bin2hex(random_bytes(32));
            // Salvar em arquivo .env se possível
            self::saveToEnvFile($key, $value);
        }

        return $value;
    }
    
    /**
     * Gerar secret seguro para webhook
     */
    private static function generateWebhookSecret() {
        return 'kesung_webhook_' . bin2hex(random_bytes(32));
    }
    
    /**
     * Gerar token seguro para webhook
     */
    private static function generateWebhookToken() {
        return hash('sha256', 'kesung-site-' . time() . '-' . bin2hex(random_bytes(16)));
    }
    
    /**
     * Salvar configuração em arquivo .env
     */
    private static function saveToEnvFile($key, $value) {
        $envFile = __DIR__ . '/../.env';
        $line = "$key=$value\n";
        
        if (is_writable(dirname($envFile))) {
            file_put_contents($envFile, $line, FILE_APPEND | LOCK_EX);
        }
    }
    
    /**
     * Obter chave de criptografia
     */
    public static function getEncryptionKey() {
        if (self::$encryptionKey === null) {
            self::$encryptionKey = self::getEnvVar('ENCRYPTION_KEY', bin2hex(random_bytes(32)));
        }
        return self::$encryptionKey;
    }
    
    /**
     * Criptografar dados sensíveis
     */
    public static function encrypt($data) {
        $key = self::getEncryptionKey();
        $iv = random_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
        return base64_encode($iv . $encrypted);
    }
    
    /**
     * Descriptografar dados sensíveis
     */
    public static function decrypt($encryptedData) {
        $key = self::getEncryptionKey();
        $data = base64_decode($encryptedData);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        return openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
    }
    
    /**
     * Validar configurações críticas
     */
    public static function validateConfig() {
        $required = ['db_host', 'db_name', 'db_user'];

        foreach ($required as $key) {
            $value = self::get($key);
            if ($value === null || $value === '') {
                throw new Exception("Configuração crítica ausente: $key");
            }
        }

        return true;
    }
    
    /**
     * Obter configurações de banco de dados
     */
    public static function getDatabaseConfig() {
        return [
            'host' => self::get('db_host'),
            'dbname' => self::get('db_name'),
            'username' => self::get('db_user'),
            'password' => self::get('db_pass'),
            'charset' => self::get('db_charset'),
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ]
        ];
    }
    
    /**
     * Obter headers seguros para APIs
     */
    public static function getSecureHeaders() {
        return [
            'X-Content-Type-Options: nosniff',
            'X-Frame-Options: DENY',
            'X-XSS-Protection: 1; mode=block',
            'Strict-Transport-Security: max-age=31536000; includeSubDomains',
            'Content-Security-Policy: default-src \'self\'; script-src \'self\' \'unsafe-inline\'; style-src \'self\' \'unsafe-inline\';',
            'Referrer-Policy: strict-origin-when-cross-origin'
        ];
    }
    
    /**
     * Aplicar headers de segurança
     */
    public static function applySecurityHeaders() {
        if (!headers_sent()) {
            foreach (self::getSecureHeaders() as $header) {
                header($header);
            }
        }
    }
    
    /**
     * Verificar se está em ambiente de produção
     */
    public static function isProduction() {
        return (self::getEnvVar('ENVIRONMENT', 'development') === 'production');
    }
    
    /**
     * Configurar ambiente seguro
     */
    public static function setupSecureEnvironment() {
        // Configurações de erro baseadas no ambiente
        if (self::isProduction()) {
            error_reporting(0);
            ini_set('display_errors', 0);
            ini_set('log_errors', 1);
        } else {
            error_reporting(E_ALL);
            ini_set('display_errors', 1);
        }
        
        // Configurações de sessão seguras
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
        ini_set('session.use_strict_mode', 1);
        ini_set('session.cookie_samesite', 'Strict');
        
        // Aplicar headers de segurança
        self::applySecurityHeaders();
        
        // Validar configurações
        self::validateConfig();
    }
}

// Inicializar ambiente seguro automaticamente
try {
    SecureConfig::setupSecureEnvironment();
} catch (Exception $e) {
    error_log("Erro na configuração de segurança: " . $e->getMessage());
    if (!SecureConfig::isProduction()) {
        die("Erro na configuração de segurança: " . $e->getMessage());
    }
}
?>
