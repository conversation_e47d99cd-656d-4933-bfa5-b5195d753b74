<?php
/**
 * Carregador de variáveis de ambiente (.env)
 * Sistema KESUNG SITE
 */

class EnvLoader {
    private static $loaded = false;
    private static $variables = [];

    /**
     * Carregar variáveis do arquivo .env
     */
    public static function load($envPath = null) {
        if (self::$loaded) {
            return;
        }

        // Determinar caminho do arquivo .env
        if ($envPath === null) {
            $envPath = self::findEnvFile();
        }

        if (!file_exists($envPath)) {
            throw new Exception("Arquivo .env não encontrado em: {$envPath}");
        }

        // Ler e processar arquivo .env
        $lines = file($envPath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        foreach ($lines as $line) {
            // Ignorar comentários
            if (strpos(trim($line), '#') === 0) {
                continue;
            }

            // Processar linha com formato KEY=VALUE
            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                $key = trim($key);
                $value = trim($value);

                // Remover aspas se existirem
                $value = trim($value, '"\'');

                // Armazenar variável
                self::$variables[$key] = $value;
                
                // Definir como variável de ambiente do PHP
                $_ENV[$key] = $value;
                putenv("{$key}={$value}");
            }
        }

        self::$loaded = true;
    }

    /**
     * Encontrar arquivo .env na hierarquia de diretórios
     */
    private static function findEnvFile() {
        $currentDir = __DIR__;
        
        // Tentar diferentes localizações
        $possiblePaths = [
            dirname($currentDir) . '/.env',           // Raiz do projeto
            dirname(dirname($currentDir)) . '/.env',  // Dois níveis acima
            $currentDir . '/.env',                    // Diretório atual
        ];

        foreach ($possiblePaths as $path) {
            if (file_exists($path)) {
                return $path;
            }
        }

        throw new Exception("Arquivo .env não encontrado em nenhum local padrão");
    }

    /**
     * Obter valor de variável de ambiente
     */
    public static function get($key, $default = null) {
        // Carregar .env se ainda não foi carregado
        if (!self::$loaded) {
            self::load();
        }

        // Verificar em ordem de prioridade:
        // 1. Variáveis de ambiente do sistema
        // 2. Variáveis carregadas do .env
        // 3. Valor padrão

        if (isset($_ENV[$key])) {
            return $_ENV[$key];
        }

        if (isset(self::$variables[$key])) {
            return self::$variables[$key];
        }

        return $default;
    }

    /**
     * Verificar se variável existe
     */
    public static function has($key) {
        if (!self::$loaded) {
            self::load();
        }

        return isset($_ENV[$key]) || isset(self::$variables[$key]);
    }

    /**
     * Obter todas as variáveis carregadas
     */
    public static function all() {
        if (!self::$loaded) {
            self::load();
        }

        return self::$variables;
    }

    /**
     * Recarregar arquivo .env
     */
    public static function reload($envPath = null) {
        self::$loaded = false;
        self::$variables = [];
        self::load($envPath);
    }
}

// Auto-carregar .env quando arquivo for incluído
try {
    EnvLoader::load();
} catch (Exception $e) {
    // Em caso de erro, usar valores padrão
    error_log("Erro ao carregar .env: " . $e->getMessage());
}
?>
