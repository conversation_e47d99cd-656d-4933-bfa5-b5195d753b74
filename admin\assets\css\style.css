/* ========== ESTILOS COMPLEMENTARES PARA ADMIN ========== */
/* Este arquivo complementa o standardized.css */

/* Navegação Sidebar Específica */
.nav-sidebar,
.nav-sidebar * {
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
}

.nav-sidebar .nav-link {
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    color: #212529 !important;
    padding: 12px 20px !important;
    border-left: 4px solid transparent !important;
    height: 50px !important;
    display: flex !important;
    align-items: center !important;
    text-decoration: none !important;
    font-size: 14px !important;
}

.nav-sidebar .nav-link:hover {
    background-color: rgba(25, 135, 84, 0.1) !important;
    color: #198754 !important;
    border-left-color: rgba(25, 135, 84, 0.3) !important;
}

.nav-sidebar .nav-link.active {
    background-color: #198754 !important;
    color: white !important;
    border-left-color: #157347 !important;
}

/* Ícones do Menu */
.nav-sidebar .nav-icon {
    margin-right: 12px !important;
    width: 20px !important;
    text-align: center !important;
    font-size: 16px !important;
    color: #6c757d !important;
}

.nav-sidebar .nav-link:hover .nav-icon {
    color: #198754 !important;
}

.nav-sidebar .nav-link.active .nav-icon {
    color: white !important;
}

/* Ajuste para submenus */
.nav-sidebar .nav-treeview {
    margin-left: 20px !important;
}

.nav-sidebar .nav-treeview .nav-link {
    padding-left: 40px !important;
    font-size: 13px !important;
    height: 45px !important;
}

/* Remover outline ao clicar */
.nav-sidebar .nav-link:focus {
    outline: none !important;
    box-shadow: none !important;
}

/* Estados específicos */
.nav-sidebar .nav-link:not(.active) {
    background-color: transparent !important;
    color: #212529 !important;
}

/* ========== MODAIS ESPECÍFICOS ========== */

/* Modal personalizado para visualização */
.modal-custom {
    background: rgba(0, 0, 0, 0.6) !important;
    backdrop-filter: blur(2px) !important;
}

.modal-dialog-custom {
    margin: 20px auto !important;
    width: 95% !important;
    max-width: 500px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-height: calc(100vh - 40px) !important;
}

.modal-content-custom {
    background: white !important;
    border: none !important;
    border-radius: 12px !important;
    position: relative !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
    overflow: hidden !important;
}

.modal-header-custom {
    border: none !important;
    padding: 20px 24px !important;
    background: #f8f9fa !important;
    border-bottom: 1px solid #e2e8f0 !important;
    position: relative !important;
}

.modal-title-custom {
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #212529 !important;
    margin: 0 !important;
}

.modal-body-custom {
    padding: 24px !important;
    text-align: center !important;
    background: white !important;
}

.modal-body-custom img {
    max-width: 100% !important;
    height: auto !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

.btn-close-custom {
    position: absolute !important;
    top: 15px !important;
    right: 15px !important;
    width: 32px !important;
    height: 32px !important;
    background: #6c757d !important;
    border: none !important;
    color: white !important;
    font-size: 16px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    z-index: 1050 !important;
}

.btn-close-custom:hover {
    background: #495057 !important;
    transform: scale(1.1) !important;
}

/* Botões do Modal Padronizados */
.modal-footer-custom {
    border: none !important;
    justify-content: center !important;
    padding: 20px 24px !important;
    background: #f8f9fa !important;
    border-top: 1px solid #e2e8f0 !important;
    gap: 12px !important;
}

.modal-footer-custom .btn {
    height: 40px !important;
    min-width: 100px !important;
    padding: 8px 20px !important;
    border-radius: 8px !important;
    font-weight: 500 !important;
    font-size: 14px !important;
    transition: all 0.3s ease !important;
}

.btn-accept-custom {
    background: #198754 !important;
    color: white !important;
    border: none !important;
}

.btn-accept-custom:hover {
    background: #157347 !important;
    transform: translateY(-1px) !important;
}

.btn-reject-custom {
    background: #dc3545 !important;
    color: white !important;
    border: none !important;
}

.btn-reject-custom:hover {
    background: #bb2d3b !important;
    transform: translateY(-1px) !important;
}

/* Responsividade para modais */
@media (min-width: 768px) {
    .modal-dialog-custom {
        max-width: 600px !important;
    }
}

@media (min-width: 992px) {
    .modal-dialog-custom {
        max-width: 800px !important;
    }
}

@media (min-width: 1200px) {
    .modal-dialog-custom {
        max-width: 900px !important;
    }
}

@media (max-width: 576px) {
    .modal-dialog-custom {
        margin: 10px !important;
        width: calc(100% - 20px) !important;
        min-height: calc(100vh - 20px) !important;
    }

    .modal-header-custom,
    .modal-body-custom,
    .modal-footer-custom {
        padding: 16px !important;
    }

    .btn-close-custom {
        top: 10px !important;
        right: 10px !important;
        width: 28px !important;
        height: 28px !important;
        font-size: 14px !important;
    }
}

/* ========== FIM DOS ESTILOS COMPLEMENTARES ========== */
