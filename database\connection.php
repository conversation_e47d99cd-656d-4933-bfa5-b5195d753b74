<?php
/**
 * Classe de Conexão com Banco de Dados - Padronizada
 * Sistema: kesung-site
 * Padrão: Singleton <PERSON>tern
 * Configurações via arquivo .env
 */

// Carregar variáveis de ambiente
require_once __DIR__ . '/../includes/env_loader.php';

class Database {
    private static $instance = null;
    private $conn;
    private const DB_CHARSET = 'utf8mb4';

    private function __construct() {
        try {
            // Obter configurações do .env
            $dbHost = EnvLoader::get('DB_HOST', 'localhost');
            $dbName = EnvLoader::get('DB_NAME', 'u276254152_banco_loja');
            $dbUser = EnvLoader::get('DB_USER', 'root');
            $dbPass = EnvLoader::get('DB_PASS', '');

            $dsn = "mysql:host={$dbHost};dbname={$dbName};charset=" . self::DB_CHARSET;

            $this->conn = new PDO(
                $dsn,
                $dbUser,
                $dbPass,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . self::DB_CHARSET,
                    PDO::ATTR_PERSISTENT => false
                ]
            );

            // Definir constantes do sistema - PADRONIZADO
            $this->defineSystemConstants();

            // Criar diretórios necessários
            $this->createDirectories();

        } catch(PDOException $e) {
            error_log("Database Connection Error: " . $e->getMessage());
            throw new Exception("Erro na conexão com o banco de dados: " . $e->getMessage());
        }
    }

    /**
     * Método para definir constantes do sistema
     */
    private function defineSystemConstants() {
        if (!defined('SITE_TITLE')) {
            define('SITE_TITLE', 'Kesung Site - Sistema de Vendas');
            define('SITE_DESCRIPTION', 'Sistema de vendas digital com PIX integrado');
            define('PRIMARY_COLOR', '#4318FF');
            define('SECONDARY_COLOR', '#6c757d');
            define('SUCCESS_COLOR', '#32CD32');
            define('DANGER_COLOR', '#dc3545');
        }

        // Configurações de upload padronizadas
        if (!defined('UPLOAD_DIR_ADMIN')) {
            define('UPLOAD_DIR_ADMIN', __DIR__ . '/../admin/uploads/products/');
            define('UPLOAD_DIR_FRONT', __DIR__ . '/../assets/images/products/');
            define('UPLOAD_DIR_DOWNLOADS', __DIR__ . '/../downloads/');
        }

        // Configurações PIX
        if (!defined('PIX_ENABLED')) {
            define('PIX_ENABLED', true);
            define('PIX_TIMEOUT', 300); // 5 minutos
        }
    }

    /**
     * Método para criar diretórios necessários
     */
    private function createDirectories() {
        $directories = [
            UPLOAD_DIR_ADMIN,
            UPLOAD_DIR_FRONT,
            UPLOAD_DIR_DOWNLOADS,
            __DIR__ . '/../logs/'
        ];

        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                if (!mkdir($dir, 0755, true) && !is_dir($dir)) {
                    error_log("Failed to create directory: $dir");
                }
            }
        }
    }

    /**
     * Singleton getInstance
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Retorna a conexão PDO
     */
    public function getConnection() {
        return $this->conn;
    }

    /**
     * Testa a conexão com o banco
     */
    public function testConnection() {
        try {
            $stmt = $this->conn->query("SELECT 1");
            return $stmt !== false;
        } catch (PDOException $e) {
            error_log("Database test connection failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Retorna informações da conexão
     */
    public function getConnectionInfo() {
        return [
            'host' => EnvLoader::get('DB_HOST', 'localhost'),
            'database' => EnvLoader::get('DB_NAME', 'u276254152_banco_loja'),
            'user' => EnvLoader::get('DB_USER', 'root'),
            'charset' => self::DB_CHARSET,
            'connected' => $this->testConnection()
        ];
    }

    // Previne clonagem
    private function __clone() {}

    // Previne deserialização
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

// Funções úteis globais
if (!function_exists('formatPrice')) {
    function formatPrice($price) {
        return number_format($price, 2, ',', '.');
    }
}

if (!function_exists('sanitizeString')) {
    function sanitizeString($str) {
        return htmlspecialchars(trim($str), ENT_QUOTES, 'UTF-8');
    }
}
